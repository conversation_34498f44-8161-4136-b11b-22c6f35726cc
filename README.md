# نظام إدارة مركز صيانة السيارات

نظام شامل لإدارة مركز صيانة السيارات مطور بلغة Python باستخدام Flask مع واجهة مستخدم متجاوبة بـ Bootstrap.

## المميزات الرئيسية

### 1. إدارة العملاء
- تسجيل بيانات العملاء (الاسم، رقم الهاتف، العنوان، البريد الإلكتروني)
- البحث عن العملاء وتعديل بياناتهم
- عرض تاريخ تعاملات العميل مع المركز

### 2. إدارة السيارات
- تسجيل بيانات السيارات (الماركة، الموديل، سنة الصنع، رقم اللوحة، رقم الشاسيه)
- ربط السيارات بأصحابها
- تسجيل قراءة العداد في كل زيارة

### 3. إدارة الصيانة
- تسجيل طلبات الصيانة الجديدة
- تحديد نوع الصيانة (دورية، إصلاح، استبدال قطع غيار)
- تتبع حالة الصيانة (قيد الانتظار، قيد التنفيذ، مكتملة)
- تسجيل الفني المسؤول عن الصيانة

### 4. إدارة قطع الغيار
- تسجيل المخزون من قطع الغيار
- تتبع استهلاك قطع الغيار
- تنبيهات عند انخفاض المخزون
- إدارة الموردين وطلبات الشراء

### 5. إدارة الفواتير والمدفوعات
- إنشاء فواتير تفصيلية للخدمات وقطع الغيار
- تسجيل المدفوعات
- إصدار تقارير مالية

### 6. التقارير والإحصائيات
- تقارير يومية/أسبوعية/شهرية للصيانة
- تقارير الإيرادات والمصروفات
- تقارير أداء الفنيين
- تقارير قطع الغيار الأكثر استخداماً

## التقنيات المستخدمة

- **Backend**: Python 3.13, Flask
- **Database**: SQLite مع SQLAlchemy ORM
- **Frontend**: HTML5, CSS3, Bootstrap 5, JavaScript
- **Authentication**: Flask-Login
- **Forms**: Flask-WTF, WTForms

## متطلبات التشغيل

```
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-WTF==1.1.1
WTForms==3.0.1
Flask-Login==0.6.3
Werkzeug==2.3.7
python-dateutil==2.8.2
email_validator==2.2.0
```

## طريقة التثبيت والتشغيل

### ⭐ الطريقة الموصى بها: لوحة التحكم المحسنة 🚀
```bash
python run_dashboard.py
```
هذا الملف سيقوم بـ:
- تشغيل النظام مع لوحة التحكم المحسنة
- عرض بيانات تجريبية واقعية
- واجهة مستخدم محسنة وواضحة
- نظام مصادقة مبسط

### 🔧 اختبار سريع للنظام:
```bash
python test_simple.py
```
للتأكد من أن النظام يعمل بشكل صحيح

### 📱 التطبيق الكامل (قد يحتاج إصلاحات):
```bash
# للنسخة الكاملة (قد تحتاج إصلاحات)
python app.py

# أو للنسخة المبسطة
python simple_app.py

# أو النسخة المصححة
python simple_app_fixed.py
```

### 🖥️ Windows (ملف Batch):
```bash
start.bat
```

### 3. الوصول للنظام
- **الرابط:** `http://127.0.0.1:5000`
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`
- **المتصفح:** أي متصفح حديث (Chrome, Firefox, Safari, Edge)

## هيكل المشروع (بعد التنظيف)

```
car center/
├── app.py                    # التطبيق الرئيسي الكامل
├── run_dashboard.py          # لوحة التحكم المحسنة
├── run_final_fixed.py        # النسخة النهائية المصححة ⭐
├── start.bat                 # ملف تشغيل Windows
├── add_sample_data.py        # إضافة بيانات تجريبية
├── models.py                 # نماذج قاعدة البيانات
├── forms.py                  # نماذج الإدخال
├── config.py                 # إعدادات التطبيق
├── requirements.txt          # المتطلبات
├── README.md                 # دليل المستخدم
├── templates/                # قوالب HTML
│   ├── base_simple.html     # القالب الأساسي مع CSS مدمج ⭐
│   ├── login_simple.html    # صفحة تسجيل الدخول
│   ├── dashboard_simple.html # لوحة التحكم المحسنة ⭐
│   ├── customers/           # قوالب العملاء
│   ├── vehicles/            # قوالب السيارات
│   ├── service_requests/    # قوالب طلبات الصيانة
│   ├── services/            # قوالب الخدمات
│   ├── parts/               # قوالب قطع الغيار
│   ├── inventory/           # قوالب المخزون
│   ├── invoices/            # قوالب الفواتير
│   ├── payments/            # قوالب المدفوعات
│   ├── technicians/         # قوالب الفنيين
│   └── reports/             # قوالب التقارير
└── static/                  # الملفات الثابتة (اختيارية)
    ├── css/
    └── js/
```

## قاعدة البيانات

النظام يستخدم قاعدة بيانات SQLite مع الجداول التالية:
- `user` - المستخدمين
- `customer` - العملاء
- `vehicle` - السيارات
- `technician` - الفنيين
- `service` - الخدمات
- `part` - قطع الغيار
- `inventory` - المخزون
- `service_request` - طلبات الصيانة
- `invoice` - الفواتير
- `invoice_detail` - تفاصيل الفواتير
- `payment` - المدفوعات

## الواجهات المتاحة

1. **لوحة التحكم الرئيسية** - عرض الإحصائيات والأنشطة الحديثة
2. **إدارة العملاء** - إضافة وتعديل وحذف العملاء
3. **إدارة السيارات** - تسجيل السيارات وربطها بالعملاء
4. **إدارة طلبات الصيانة** - تتبع حالة الصيانة
5. **إدارة المخزون** - متابعة قطع الغيار
6. **إدارة الفواتير** - إنشاء ومتابعة الفواتير
7. **التقارير** - تقارير شاملة للأعمال

## المطور

تم تطوير هذا النظام باستخدام أفضل الممارسات في تطوير تطبيقات الويب مع التركيز على:
- سهولة الاستخدام
- التصميم المتجاوب
- الأمان
- قابلية التوسع

## الإنجازات والتحديثات الأخيرة ✨

### ✅ المميزات المكتملة:
- **إصلاح مشاكل CSS** - CSS مدمج يعمل بشكل مثالي ⭐
- **لوحة التحكم المحسنة** - تصميم جديد مع ألوان متدرجة
- **تنظيف المشروع** - إزالة الملفات المتكررة والغير ضرورية
- **نظام التشغيل المصحح** - `run_final_fixed.py` يعمل بدون مشاكل
- **قوالب محسنة** - `base_simple.html` و `dashboard_simple.html`
- **نظام إدارة الفنيين** - إضافة وتعديل وتتبع أداء الفنيين
- **نظام الفواتير المتقدم** - فواتير تفصيلية مع فلترة وبحث
- **معالجة الأخطاء** - نظام محسن لمعالجة الأخطاء والتنبيهات

### 🔄 قيد التطوير:
- نظام المدفوعات التفصيلي
- تقارير متقدمة مع رسوم بيانية
- نظام الإشعارات والتنبيهات
- واجهة برمجة التطبيقات (API)
- نظام النسخ الاحتياطي

### 📊 إحصائيات المشروع (بعد التنظيف):
- **عدد الملفات الأساسية:** 8 ملفات
- **أسطر الكود:** 2500+ سطر
- **الواجهات:** 15+ صفحة
- **المميزات:** 20+ ميزة
- **الملفات المحذوفة:** 12 ملف متكرر

## ملاحظات تقنية

- **اللغة:** النظام يدعم اللغة العربية بالكامل مع RTL
- **التوافق:** متوافق مع جميع المتصفحات الحديثة
- **الأمان:** تشفير كلمات المرور وحماية من CSRF
- **الأداء:** محسن للسرعة والاستجابة
- **التوسع:** قابل للتوسع بسهولة لإضافة مميزات جديدة
- **قاعدة البيانات:** تُنشأ تلقائياً عند التشغيل الأول
