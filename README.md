# نظام إدارة مركز صيانة السيارات

نظام شامل لإدارة مركز صيانة السيارات مطور بلغة Python باستخدام Flask مع واجهة مستخدم متجاوبة بـ Bootstrap.

## المميزات الرئيسية

### 1. إدارة العملاء
- تسجيل بيانات العملاء (الاسم، رقم الهاتف، العنوان، البريد الإلكتروني)
- البحث عن العملاء وتعديل بياناتهم
- عرض تاريخ تعاملات العميل مع المركز

### 2. إدارة السيارات
- تسجيل بيانات السيارات (الماركة، الموديل، سنة الصنع، رقم اللوحة، رقم الشاسيه)
- ربط السيارات بأصحابها
- تسجيل قراءة العداد في كل زيارة

### 3. إدارة الصيانة
- تسجيل طلبات الصيانة الجديدة
- تحديد نوع الصيانة (دورية، إصلاح، استبدال قطع غيار)
- تتبع حالة الصيانة (قيد الانتظار، قيد التنفيذ، مكتملة)
- تسجيل الفني المسؤول عن الصيانة

### 4. إدارة قطع الغيار
- تسجيل المخزون من قطع الغيار
- تتبع استهلاك قطع الغيار
- تنبيهات عند انخفاض المخزون
- إدارة الموردين وطلبات الشراء

### 5. إدارة الفواتير والمدفوعات
- إنشاء فواتير تفصيلية للخدمات وقطع الغيار
- تسجيل المدفوعات
- إصدار تقارير مالية

### 6. التقارير والإحصائيات
- تقارير يومية/أسبوعية/شهرية للصيانة
- تقارير الإيرادات والمصروفات
- تقارير أداء الفنيين
- تقارير قطع الغيار الأكثر استخداماً

## التقنيات المستخدمة

- **Backend**: Python 3.13, Flask
- **Database**: SQLite مع SQLAlchemy ORM
- **Frontend**: HTML5, CSS3, Bootstrap 5, JavaScript
- **Authentication**: Flask-Login
- **Forms**: Flask-WTF, WTForms

## متطلبات التشغيل

```
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-WTF==1.1.1
WTForms==3.0.1
Flask-Login==0.6.3
Werkzeug==2.3.7
python-dateutil==2.8.2
email_validator==2.2.0
```

## طريقة التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. تشغيل التطبيق
```bash
python app.py
```

أو للتشغيل المبسط:
```bash
python simple_app.py
```

### 3. الوصول للنظام
- افتح المتصفح وانتقل إلى: `http://127.0.0.1:5000`
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

## هيكل المشروع

```
car center/
├── app.py                 # التطبيق الرئيسي
├── simple_app.py          # نسخة مبسطة للتشغيل السريع
├── models.py              # نماذج قاعدة البيانات
├── forms.py               # نماذج الإدخال
├── config.py              # إعدادات التطبيق
├── requirements.txt       # المتطلبات
├── templates/             # قوالب HTML
│   ├── base.html         # القالب الأساسي
│   ├── login.html        # صفحة تسجيل الدخول
│   ├── dashboard.html    # لوحة التحكم
│   ├── customers/        # قوالب العملاء
│   ├── vehicles/         # قوالب السيارات
│   ├── service_requests/ # قوالب طلبات الصيانة
│   ├── services/         # قوالب الخدمات
│   ├── parts/            # قوالب قطع الغيار
│   ├── inventory/        # قوالب المخزون
│   ├── invoices/         # قوالب الفواتير
│   ├── payments/         # قوالب المدفوعات
│   └── reports/          # قوالب التقارير
└── static/               # الملفات الثابتة
    ├── css/
    │   └── style.css     # التنسيقات المخصصة
    └── js/
        └── main.js       # JavaScript مخصص
```

## قاعدة البيانات

النظام يستخدم قاعدة بيانات SQLite مع الجداول التالية:
- `user` - المستخدمين
- `customer` - العملاء
- `vehicle` - السيارات
- `technician` - الفنيين
- `service` - الخدمات
- `part` - قطع الغيار
- `inventory` - المخزون
- `service_request` - طلبات الصيانة
- `invoice` - الفواتير
- `invoice_detail` - تفاصيل الفواتير
- `payment` - المدفوعات

## الواجهات المتاحة

1. **لوحة التحكم الرئيسية** - عرض الإحصائيات والأنشطة الحديثة
2. **إدارة العملاء** - إضافة وتعديل وحذف العملاء
3. **إدارة السيارات** - تسجيل السيارات وربطها بالعملاء
4. **إدارة طلبات الصيانة** - تتبع حالة الصيانة
5. **إدارة المخزون** - متابعة قطع الغيار
6. **إدارة الفواتير** - إنشاء ومتابعة الفواتير
7. **التقارير** - تقارير شاملة للأعمال

## المطور

تم تطوير هذا النظام باستخدام أفضل الممارسات في تطوير تطبيقات الويب مع التركيز على:
- سهولة الاستخدام
- التصميم المتجاوب
- الأمان
- قابلية التوسع

## ملاحظات

- النظام يدعم اللغة العربية بالكامل
- التصميم متجاوب ويعمل على جميع الأجهزة
- يمكن توسيع النظام بسهولة لإضافة مميزات جديدة
- قاعدة البيانات تُنشأ تلقائياً عند التشغيل الأول
