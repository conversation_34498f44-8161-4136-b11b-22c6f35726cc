# 🚗 مركز صيانة السيارات - Car Center Management System

## 📋 نظرة عامة / Overview

نظام إدارة شامل لمركز صيانة السيارات يتضمن إدارة العملاء والصيانة والمالية.

A comprehensive management system for car service centers including customer, maintenance, and financial management.

## ✨ الميزات الرئيسية / Key Features

### 🏠 لوحة التحكم / Dashboard
- عرض الإحصائيات العامة
- الأنشطة الحديثة
- تنبيهات المخزون

### 👥 إدارة العملاء / Customer Management
- عرض قائمة العملاء مع البحث
- **إضافة عميل جديد مدمج في نفس الصفحة**
- تصنيف العملاء (عادي، مميز، VIP)

### 🔧 إدارة الصيانة / Maintenance Management
- عرض طلبات الصيانة مع البحث
- **إضافة طلب صيانة جديد مدمج في نفس الصفحة**
- تتبع حالة الصيانة والأولوية
- تخصيص الفنيين

### 💰 إدارة المالية / Financial Management
- عرض الفواتير والمدفوعات
- تتبع حالات الدفع
- إحصائيات مالية شاملة

## 🚀 التشغيل السريع / Quick Start

### 1. تشغيل التطبيق / Run Application
```bash
# الطريقة الأولى / Method 1
python run_final_app.py

# الطريقة الثانية (Windows) / Method 2 (Windows)
run_final.bat
```

### 2. الوصول للتطبيق / Access Application
افتح المتصفح على: **http://127.0.0.1:5000**

### 3. تسجيل الدخول / Login
- **المستخدم / Username:** `admin`
- **كلمة المرور / Password:** `admin123`

## 📁 هيكل المشروع / Project Structure

```
car-center/
├── final_app.py              # التطبيق الرئيسي / Main application
├── run_final_app.py          # ملف التشغيل / Run script
├── run_final.bat             # ملف تشغيل Windows / Windows batch
├── test_final_app.py         # ملف الاختبار / Test script
├── README.md                 # هذا الملف / This file
├── FINAL_APP_INSTRUCTIONS.md # دليل مفصل / Detailed guide
└── INTEGRATED_FORMS_UPDATE.md # تحديث دمج النماذج / Forms integration update
```

## 🎯 الميزات الجديدة / New Features

### ✅ النماذج المدمجة / Integrated Forms
- **صفحة العملاء**: تحتوي على قائمة العملاء + نموذج إضافة عميل
- **صفحة الصيانة**: تحتوي على قائمة الصيانة + نموذج إضافة طلب صيانة
- **تمرير سلس**: أزرار تنقل تلقائياً إلى النماذج
- **تحقق من البيانات**: فحص صحة المدخلات قبل الحفظ

### 🎨 تحسينات التصميم / Design Improvements
- تصميم متجاوب مع Bootstrap 5
- ألوان مميزة لكل قسم
- أيقونات وصفية لجميع العناصر
- رسائل تفاعلية للنجاح والأخطاء

## 🧪 اختبار التطبيق / Testing

```bash
python test_final_app.py
```

## 📖 الدلائل المفصلة / Detailed Guides

- **[دليل الاستخدام الكامل](FINAL_APP_INSTRUCTIONS.md)** - شرح مفصل لجميع الميزات
- **[تحديث دمج النماذج](INTEGRATED_FORMS_UPDATE.md)** - تفاصيل التحديثات الأخيرة

## 🔧 المتطلبات / Requirements

- Python 3.7+
- Flask
- Bootstrap 5 (يتم تحميله من CDN)

## 📝 ملاحظات مهمة / Important Notes

1. **بيانات تجريبية**: التطبيق يستخدم بيانات تجريبية مؤقتة
2. **لا يحتاج قاعدة بيانات**: جميع البيانات في الذاكرة
3. **مناسب للعرض**: مثالي لعرض الميزات والتجربة
4. **إعادة التشغيل**: البيانات تختفي عند إعادة تشغيل التطبيق

## 🎉 الإنجازات / Achievements

✅ **تم حل خطأ UnboundLocalError**  
✅ **دمج نماذج الإضافة في الصفحات الرئيسية**  
✅ **تحسين تجربة المستخدم**  
✅ **تنظيف المشروع من الملفات المتكررة**  
✅ **تطبيق جاهز للاستخدام والعرض**  

## 📞 الدعم / Support

للمساعدة أو الاستفسارات، راجع الملفات التوثيقية المرفقة أو اختبر التطبيق مباشرة.

---

**🚗 مركز صيانة السيارات - نظام إدارة متكامل وسهل الاستخدام**
