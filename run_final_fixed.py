#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إدارة مركز صيانة السيارات - النسخة النهائية المصححة
Car Center Management System - Final Fixed Version
"""

from flask import Flask, render_template, redirect, url_for, flash, request, session
from datetime import datetime
import webbrowser
import threading
import time

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'car-center-secret-key-2024'

# Simple authentication
def is_logged_in():
    return session.get('logged_in', False)

def require_login():
    if not is_logged_in():
        flash('يرجى تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))
    return None

# Auto-open browser
def open_browser():
    time.sleep(1.5)  # Wait for server to start
    webbrowser.open('http://127.0.0.1:5000')

# Routes
@app.route('/')
def index():
    if is_logged_in():
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if is_logged_in():
        return redirect(url_for('dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        # Simple authentication
        if username == 'admin' and password == 'admin123':
            session['logged_in'] = True
            session['username'] = username
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('login_simple.html')

@app.route('/dashboard')
def dashboard():
    auth_check = require_login()
    if auth_check:
        return auth_check
    
    # بيانات تجريبية لعرض لوحة التحكم
    stats = {
        'total_customers': 45,
        'total_vehicles': 67,
        'pending_requests': 8,
        'monthly_revenue': 25000
    }
    
    # طلبات صيانة تجريبية
    class MockRequest:
        def __init__(self, customer_name, car_make, car_model, date, status):
            self.vehicle = MockVehicle(customer_name, car_make, car_model)
            self.request_date = datetime.strptime(date, '%Y-%m-%d')
            self.status = status
    
    class MockVehicle:
        def __init__(self, owner_name, make, model):
            self.owner = MockOwner(owner_name)
            self.make = make
            self.model = model
    
    class MockOwner:
        def __init__(self, name):
            self.name = name
    
    recent_requests = [
        MockRequest("أحمد محمد", "تويوتا", "كامري", "2024-01-15", "pending"),
        MockRequest("سعد العتيبي", "هوندا", "أكورد", "2024-01-14", "in_progress"),
        MockRequest("محمد الغامدي", "نيسان", "التيما", "2024-01-13", "completed"),
        MockRequest("خالد القحطاني", "هيونداي", "إلنترا", "2024-01-12", "pending"),
        MockRequest("فهد الشمري", "كيا", "أوبتيما", "2024-01-11", "completed"),
    ]
    
    # عناصر مخزون منخفضة تجريبية
    class MockInventoryItem:
        def __init__(self, part_name, part_number, quantity, min_quantity):
            self.part = MockPart(part_name, part_number)
            self.quantity = quantity
            self.min_quantity = min_quantity
    
    class MockPart:
        def __init__(self, name, part_number):
            self.name = name
            self.part_number = part_number
    
    low_stock_items = [
        MockInventoryItem("زيت محرك", "OIL001", 5, 10),
        MockInventoryItem("فلتر هواء", "AIR001", 3, 15),
        MockInventoryItem("فحمات فرامل", "BRK001", 2, 8),
    ]
    
    return render_template('dashboard_simple.html',
                         stats=stats,
                         recent_requests=recent_requests,
                         low_stock_items=low_stock_items)

# Navigation routes with flash messages
@app.route('/customers')
def customers():
    auth_check = require_login()
    if auth_check:
        return auth_check
    flash('صفحة إدارة العملاء قيد التطوير', 'info')
    return redirect(url_for('dashboard'))

@app.route('/vehicles')
def vehicles():
    auth_check = require_login()
    if auth_check:
        return auth_check
    flash('صفحة إدارة السيارات قيد التطوير', 'info')
    return redirect(url_for('dashboard'))

@app.route('/service_requests')
def service_requests():
    auth_check = require_login()
    if auth_check:
        return auth_check
    flash('صفحة إدارة طلبات الصيانة قيد التطوير', 'info')
    return redirect(url_for('dashboard'))

@app.route('/parts')
def parts():
    auth_check = require_login()
    if auth_check:
        return auth_check
    flash('صفحة إدارة قطع الغيار قيد التطوير', 'info')
    return redirect(url_for('dashboard'))

@app.route('/inventory')
def inventory():
    auth_check = require_login()
    if auth_check:
        return auth_check
    flash('صفحة إدارة المخزون قيد التطوير', 'info')
    return redirect(url_for('dashboard'))

@app.route('/invoices')
def invoices():
    auth_check = require_login()
    if auth_check:
        return auth_check
    flash('صفحة إدارة الفواتير قيد التطوير', 'info')
    return redirect(url_for('dashboard'))

@app.route('/reports')
def reports():
    auth_check = require_login()
    if auth_check:
        return auth_check
    flash('صفحة التقارير قيد التطوير', 'info')
    return redirect(url_for('dashboard'))

@app.route('/add_customer')
def add_customer():
    auth_check = require_login()
    if auth_check:
        return auth_check
    flash('صفحة إضافة عميل جديد قيد التطوير', 'info')
    return redirect(url_for('dashboard'))

@app.route('/add_vehicle')
def add_vehicle():
    auth_check = require_login()
    if auth_check:
        return auth_check
    flash('صفحة إضافة سيارة جديدة قيد التطوير', 'info')
    return redirect(url_for('dashboard'))

@app.route('/add_service_request')
def add_service_request():
    auth_check = require_login()
    if auth_check:
        return auth_check
    flash('صفحة طلب صيانة جديد قيد التطوير', 'info')
    return redirect(url_for('dashboard'))

@app.route('/technicians')
def technicians():
    auth_check = require_login()
    if auth_check:
        return auth_check
    flash('صفحة إدارة الفنيين قيد التطوير', 'info')
    return redirect(url_for('dashboard'))

@app.route('/add_invoice')
def add_invoice():
    auth_check = require_login()
    if auth_check:
        return auth_check
    flash('صفحة إنشاء فاتورة جديدة قيد التطوير', 'info')
    return redirect(url_for('dashboard'))

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

if __name__ == '__main__':
    print("=" * 60)
    print("🚗 نظام إدارة مركز صيانة السيارات")
    print("Car Center Management System")
    print("=" * 60)
    print("🚀 بدء تشغيل النظام...")
    print("📍 الرابط: http://127.0.0.1:5000")
    print("👤 المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("🎨 لوحة التحكم المحسنة مع CSS مدمج")
    print("✅ تم إصلاح مشاكل CSS")
    print("=" * 60)
    print("🌐 سيتم فتح المتصفح تلقائياً...")
    
    # Start browser in a separate thread
    threading.Thread(target=open_browser, daemon=True).start()
    
    # Run the app
    app.run(debug=True, host='127.0.0.1', port=5000, use_reloader=False)
