#!/usr/bin/env python3
# -*- coding: utf-8 -*-

try:
    print("بدء تشغيل نظام إدارة مركز صيانة السيارات...")
    print("=" * 50)
    
    # Import modules
    print("1. استيراد المكتبات...")
    from flask import Flask
    print("   ✓ Flask imported")
    
    from models import db
    print("   ✓ Models imported")
    
    from config import Config
    print("   ✓ Config imported")
    
    # Import app
    print("2. استيراد التطبيق...")
    import app
    print("   ✓ App imported successfully")
    
    # Create database
    print("3. إنشاء قاعدة البيانات...")
    with app.app.app_context():
        db.create_all()
        print("   ✓ Database created")
    
    print("4. بدء تشغيل الخادم...")
    print("   الخادم يعمل على: http://127.0.0.1:5000")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    print("=" * 50)
    
    # Start the app
    app.app.run(debug=True, host='127.0.0.1', port=5000)
    
except Exception as e:
    print(f"خطأ في تشغيل التطبيق: {e}")
    import traceback
    traceback.print_exc()
