{% extends "base.html" %}

{% block title %}المدفوعات - مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-credit-card me-2"></i>
            المدفوعات
        </h1>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if payments %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>تاريخ الدفع</th>
                                <th>رقم المرجع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in payments %}
                            <tr>
                                <td>{{ payment.invoice.invoice_number }}</td>
                                <td>{{ payment.invoice.customer.name }}</td>
                                <td class="currency">{{ payment.amount }}</td>
                                <td>
                                    {% if payment.payment_method == 'cash' %}
                                        <span class="badge bg-success">نقدي</span>
                                    {% elif payment.payment_method == 'card' %}
                                        <span class="badge bg-info">بطاقة</span>
                                    {% elif payment.payment_method == 'bank_transfer' %}
                                        <span class="badge bg-primary">تحويل بنكي</span>
                                    {% endif %}
                                </td>
                                <td>{{ payment.payment_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ payment.reference_number or '-' }}</td>
                                <td>
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-credit-card display-1 text-muted"></i>
                    <h4 class="mt-3">لا توجد مدفوعات</h4>
                    <p class="text-muted">لم يتم تسجيل أي مدفوعات بعد</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
