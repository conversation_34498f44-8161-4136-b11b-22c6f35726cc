#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح دالة الصيانة
"""

from flask import Flask, redirect, url_for, flash, request, session

def create_maintenance_function():
    def maintenance():
        # التحقق من تسجيل الدخول
        if not session.get('logged_in', False):
            flash('يرجى تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
            return redirect(url_for('login'))

        # معالجة إضافة طلب صيانة جديد
        if request.method == 'POST':
            customer_name = request.form.get('customer_name')
            phone = request.form.get('phone')
            vehicle_info = request.form.get('vehicle_info')
            service_type = request.form.get('service_type')
            description = request.form.get('description')
            priority = request.form.get('priority')
            technician = request.form.get('technician')
            scheduled_date = request.form.get('scheduled_date')
            estimated_cost = request.form.get('estimated_cost')
            notes = request.form.get('notes')

            # التحقق من صحة البيانات
            errors = []

            if not customer_name or len(customer_name.strip()) < 3:
                errors.append('اسم العميل مطلوب')

            if not phone or len(phone) != 10 or not phone.startswith('05'):
                errors.append('رقم الهاتف يجب أن يكون 10 أرقام ويبدأ بـ 05')

            if not vehicle_info:
                errors.append('معلومات السيارة مطلوبة')

            if not service_type:
                errors.append('نوع الخدمة مطلوب')

            if errors:
                for error in errors:
                    flash(error, 'error')
            else:
                # في التطبيق الحقيقي، سيتم حفظ جميع البيانات في قاعدة البيانات
                success_msg = f'تم إضافة طلب الصيانة بنجاح للعميل: {customer_name} - {service_type}'
                flash(success_msg, 'success')
                return redirect(url_for('maintenance'))

        # بيانات صيانة تجريبية
        maintenance_requests = [
            {
                'id': 1,
                'customer_name': 'أحمد محمد العلي',
                'vehicle': 'تويوتا كامري 2020',
                'service_type': 'صيانة دورية',
                'status': 'قيد التنفيذ',
                'technician': 'محمد الفني',
                'start_date': '2024-01-15',
                'estimated_completion': '2024-01-16',
                'cost': 450.00,
                'priority': 'متوسط'
            },
            {
                'id': 2,
                'customer_name': 'سعد العتيبي',
                'vehicle': 'هوندا أكورد 2019',
                'service_type': 'إصلاح فرامل',
                'status': 'مكتمل',
                'technician': 'أحمد الفني',
                'start_date': '2024-01-14',
                'estimated_completion': '2024-01-14',
                'cost': 320.00,
                'priority': 'عالي'
            },
            {
                'id': 3,
                'customer_name': 'محمد الغامدي',
                'vehicle': 'نيسان التيما 2021',
                'service_type': 'تغيير زيت',
                'status': 'في الانتظار',
                'technician': 'خالد الفني',
                'start_date': '2024-01-16',
                'estimated_completion': '2024-01-16',
                'cost': 180.00,
                'priority': 'منخفض'
            },
            {
                'id': 4,
                'customer_name': 'خالد القحطاني',
                'vehicle': 'هيونداي إلنترا 2018',
                'service_type': 'فحص شامل',
                'status': 'قيد التنفيذ',
                'technician': 'سعد الفني',
                'start_date': '2024-01-15',
                'estimated_completion': '2024-01-17',
                'cost': 250.00,
                'priority': 'متوسط'
            },
            {
                'id': 5,
                'customer_name': 'فهد الشمري',
                'vehicle': 'كيا أوبتيما 2020',
                'service_type': 'إصلاح تكييف',
                'status': 'في الانتظار',
                'technician': 'عبدالله الفني',
                'start_date': '2024-01-17',
                'estimated_completion': '2024-01-18',
                'cost': 380.00,
                'priority': 'عالي'
            }
        ]

        # محاكاة البحث
        search_term = request.args.get('search', '') if request.args else ''
        if search_term:
            maintenance_requests = [r for r in maintenance_requests if search_term.lower() in r['customer_name'].lower() or search_term.lower() in r['vehicle'].lower()]

        return render_maintenance_page(maintenance_requests, search_term)
    
    return maintenance

def get_flash_messages():
    messages = []
    if hasattr(session, '_flashes'):
        for category, message in session._flashes:
            alert_class = 'danger' if category == 'error' else category
            messages.append(f'''
                <div class="alert alert-{alert_class} alert-dismissible fade show" role="alert">
                    <i class="bi bi-info-circle me-2"></i>
                    {message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            ''')
        session._flashes.clear()
    return ''.join(messages)

def render_maintenance_page(maintenance_requests, search_term):
    # بناء HTML لصفحة الصيانة
    flash_messages = get_flash_messages()
    
    maintenance_html = f'''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إدارة الصيانة - مركز صيانة السيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
        <style>
            body {{ background-color: #f8f9fa; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }}
            .navbar {{ box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
            .card {{ border: none; border-radius: 15px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); }}
            .btn {{ border-radius: 10px; transition: all 0.3s ease; }}
            .btn:hover {{ transform: translateY(-2px); }}
            .table {{ border-radius: 15px; overflow: hidden; }}
            .badge {{ font-size: 0.8em; }}
        </style>
    </head>
    <body>
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/dashboard">
                    <i class="bi bi-car-front me-2"></i>
                    مركز صيانة السيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/dashboard">
                        <i class="bi bi-speedometer2 me-1"></i>
                        لوحة التحكم
                    </a>
                    <a class="nav-link" href="/customers">
                        <i class="bi bi-people me-1"></i>
                        العملاء
                    </a>
                    <a class="nav-link active" href="/maintenance">
                        <i class="bi bi-tools me-1"></i>
                        الصيانة
                    </a>
                    <a class="nav-link" href="/finance">
                        <i class="bi bi-cash-coin me-1"></i>
                        المالية
                    </a>
                    <a class="nav-link" href="/add_vehicle">
                        <i class="bi bi-car-front-fill me-1"></i>
                        إضافة سيارة
                    </a>
                    <a class="nav-link" href="/add_invoice">
                        <i class="bi bi-receipt me-1"></i>
                        فاتورة جديدة
                    </a>
                    <a class="nav-link" href="/logout">
                        <i class="bi bi-box-arrow-right me-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="mb-1">
                                <i class="bi bi-tools me-2 text-primary"></i>
                                إدارة الصيانة
                            </h1>
                            <p class="text-muted mb-0">متابعة وإدارة جميع طلبات الصيانة</p>
                        </div>
                        <div>
                            <button onclick="scrollToMaintenanceForm()" class="btn btn-primary me-2">
                                <i class="bi bi-plus-circle me-2"></i>
                                طلب صيانة جديد
                            </button>
                            <button class="btn btn-outline-success" onclick="alert('قيد التطوير')">
                                <i class="bi bi-file-earmark-excel me-2"></i>
                                تصدير التقرير
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {flash_messages}
    '''
    
    return maintenance_html
