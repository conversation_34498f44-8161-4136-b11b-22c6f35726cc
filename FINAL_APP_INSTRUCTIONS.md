# مركز صيانة السيارات - التطبيق النهائي
# Car Center Management System - Final Application

## 🚗 نظرة عامة / Overview

هذا هو التطبيق النهائي لنظام إدارة مركز صيانة السيارات. يحتوي على جميع الميزات الأساسية لإدارة العملاء والسيارات والصيانة والمالية.

This is the final application for the Car Center Management System. It contains all essential features for managing customers, vehicles, maintenance, and finances.

## 🔧 المشكلة التي تم حلها / Issue Fixed

تم إصلاح خطأ `UnboundLocalError` الذي كان يحدث عند محاولة الوصول إلى متغير `search_term` في دوال:
- `maintenance()`
- `finance()`
- `customers()`

Fixed the `UnboundLocalError` that occurred when trying to access the `search_term` variable in functions:
- `maintenance()`
- `finance()`
- `customers()`

### الحل / Solution

تم إضافة فحص للتأكد من وجود `request.args` قبل محاولة الوصول إليه:

```python
# قبل / Before
search_term = request.args.get('search', '')

# بعد / After
search_term = request.args.get('search', '') if request.args else ''
```

## 🚀 كيفية التشغيل / How to Run

### الطريقة الأولى / Method 1: Using Python Script
```bash
python run_final_app.py
```

### الطريقة الثانية / Method 2: Using Batch File (Windows)
```bash
run_final.bat
```

### الطريقة الثالثة / Method 3: Direct Run
```bash
python final_app.py
```

## 📋 معلومات تسجيل الدخول / Login Information

- **اسم المستخدم / Username:** `admin`
- **كلمة المرور / Password:** `admin123`

## 🌐 الوصول للتطبيق / Accessing the Application

بعد تشغيل التطبيق، سيتم فتح المتصفح تلقائياً على:
After running the application, the browser will automatically open at:

**http://127.0.0.1:5000**

## 📱 الميزات المتاحة / Available Features

### 🏠 لوحة التحكم / Dashboard
- عرض الإحصائيات العامة
- الأنشطة الحديثة
- تنبيهات المخزون

### 👥 إدارة العملاء / Customer Management
- عرض قائمة العملاء
- إضافة عميل جديد
- البحث في العملاء

### 🔧 إدارة الصيانة / Maintenance Management
- عرض طلبات الصيانة
- إضافة طلب صيانة جديد
- تتبع حالة الصيانة
- البحث في طلبات الصيانة

### 💰 إدارة المالية / Financial Management
- عرض الفواتير
- إضافة فاتورة جديدة
- تتبع المدفوعات
- البحث في الفواتير

### ➕ إضافات أخرى / Additional Features
- إضافة سيارة جديدة
- إنشاء فاتورة جديدة
- تسجيل الخروج

## 🧪 اختبار التطبيق / Testing the Application

لاختبار التطبيق قبل التشغيل:
To test the application before running:

```bash
python test_final_app.py
```

## 📁 الملفات المهمة / Important Files

- `final_app.py` - التطبيق الرئيسي / Main application
- `run_final_app.py` - ملف التشغيل / Run script
- `run_final.bat` - ملف التشغيل لـ Windows / Windows batch file
- `test_final_app.py` - ملف الاختبار / Test script

## ⚠️ ملاحظات مهمة / Important Notes

1. هذا التطبيق يستخدم بيانات تجريبية (Mock Data)
2. لا يحتاج إلى قاعدة بيانات خارجية
3. جميع البيانات مؤقتة وتختفي عند إعادة تشغيل التطبيق
4. مناسب للعرض والتجربة

1. This application uses mock data
2. No external database required
3. All data is temporary and disappears when restarting
4. Suitable for demonstration and testing

## 🔄 إعادة التشغيل / Restart

لإيقاف التطبيق: اضغط `Ctrl+C` في Terminal
To stop the application: Press `Ctrl+C` in Terminal

لإعادة التشغيل: شغل أي من ملفات التشغيل مرة أخرى
To restart: Run any of the run files again
