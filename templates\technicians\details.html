{% extends "base.html" %}

{% block title %}تفاصيل الفني: {{ technician.name }} - مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-person-workspace me-2"></i>
                تفاصيل الفني: {{ technician.name }}
            </h1>
            <div>
                <a href="{{ url_for('edit_technician', id=technician.id) }}" class="btn btn-primary">
                    <i class="bi bi-pencil me-2"></i>
                    تعديل البيانات
                </a>
                <a href="{{ url_for('technicians') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left me-2"></i>
                    رجوع
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- معلومات الفني -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-person-badge me-2"></i>
                    المعلومات الشخصية
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center" 
                         style="width: 80px; height: 80px;">
                        <i class="bi bi-person-fill text-white" style="font-size: 2rem;"></i>
                    </div>
                    <h4 class="mt-2">{{ technician.name }}</h4>
                    {% if technician.is_active %}
                        <span class="badge bg-success">نشط</span>
                    {% else %}
                        <span class="badge bg-secondary">غير نشط</span>
                    {% endif %}
                </div>
                
                <hr>
                
                <div class="mb-2">
                    <i class="bi bi-telephone me-2 text-primary"></i>
                    <strong>الهاتف:</strong> {{ technician.phone or 'غير محدد' }}
                </div>
                <div class="mb-2">
                    <i class="bi bi-envelope me-2 text-primary"></i>
                    <strong>البريد:</strong> {{ technician.email or 'غير محدد' }}
                </div>
                <div class="mb-2">
                    <i class="bi bi-tools me-2 text-primary"></i>
                    <strong>التخصص:</strong> {{ technician.specialization or 'عام' }}
                </div>
                <div class="mb-2">
                    <i class="bi bi-calendar me-2 text-primary"></i>
                    <strong>تاريخ التوظيف:</strong> 
                    {{ technician.hire_date.strftime('%Y-%m-%d') if technician.hire_date else 'غير محدد' }}
                </div>
                {% if technician.salary %}
                <div class="mb-2">
                    <i class="bi bi-currency-dollar me-2 text-primary"></i>
                    <strong>الراتب:</strong> {{ "{:,.0f}".format(technician.salary) }} ريال
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- إحصائيات الأداء -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up me-2"></i>
                    إحصائيات الأداء
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <div class="bg-primary text-white rounded p-3">
                            <h3>{{ technician.service_requests|length }}</h3>
                            <p class="mb-0">إجمالي المهام</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="bg-success text-white rounded p-3">
                            <h3>{{ technician.service_requests|selectattr('status', 'equalto', 'completed')|list|length }}</h3>
                            <p class="mb-0">مهام مكتملة</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="bg-warning text-white rounded p-3">
                            <h3>{{ technician.service_requests|selectattr('status', 'equalto', 'in_progress')|list|length }}</h3>
                            <p class="mb-0">قيد التنفيذ</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="bg-info text-white rounded p-3">
                            <h3>{{ technician.service_requests|selectattr('status', 'equalto', 'pending')|list|length }}</h3>
                            <p class="mb-0">في الانتظار</p>
                        </div>
                    </div>
                </div>
                
                <!-- معدل الإنجاز -->
                {% set total_requests = technician.service_requests|length %}
                {% set completed_requests = technician.service_requests|selectattr('status', 'equalto', 'completed')|list|length %}
                {% if total_requests > 0 %}
                <div class="mt-3">
                    <h6>معدل الإنجاز</h6>
                    <div class="progress">
                        {% set completion_rate = (completed_requests / total_requests * 100)|round(1) %}
                        <div class="progress-bar bg-success" style="width: {{ completion_rate }}%">
                            {{ completion_rate }}%
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- طلبات الصيانة الحديثة -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-list-task me-2"></i>
                    طلبات الصيانة الحديثة
                </h5>
            </div>
            <div class="card-body">
                {% if technician.service_requests %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>العميل</th>
                                <th>السيارة</th>
                                <th>نوع الخدمة</th>
                                <th>تاريخ الطلب</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for request in technician.service_requests|sort(attribute='request_date', reverse=true)[:10] %}
                            <tr>
                                <td><strong>#{{ request.id }}</strong></td>
                                <td>{{ request.vehicle.owner.name }}</td>
                                <td>
                                    {{ request.vehicle.make }} {{ request.vehicle.model }}
                                    <br><small class="text-muted">{{ request.vehicle.plate_number }}</small>
                                </td>
                                <td>
                                    {% if request.service_type == 'maintenance' %}
                                        <span class="badge bg-info">صيانة دورية</span>
                                    {% elif request.service_type == 'repair' %}
                                        <span class="badge bg-warning">إصلاح</span>
                                    {% elif request.service_type == 'replacement' %}
                                        <span class="badge bg-secondary">استبدال قطع غيار</span>
                                    {% endif %}
                                </td>
                                <td>{{ request.request_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if request.status == 'pending' %}
                                        <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif request.status == 'in_progress' %}
                                        <span class="badge bg-info">قيد التنفيذ</span>
                                    {% elif request.status == 'completed' %}
                                        <span class="badge bg-success">مكتملة</span>
                                    {% elif request.status == 'cancelled' %}
                                        <span class="badge bg-danger">ملغية</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="#" class="btn btn-outline-info btn-sm" title="عرض التفاصيل">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-list-task display-4 text-muted"></i>
                    <h5 class="mt-3">لا توجد طلبات صيانة</h5>
                    <p class="text-muted">لم يتم تكليف هذا الفني بأي مهام بعد</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
