{% extends "base.html" %}

{% block title %}الفواتير - مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-receipt me-2"></i>
                الفواتير
            </h1>
            <button class="btn btn-primary">
                <i class="bi bi-plus-lg me-2"></i>
                إنشاء فاتورة جديدة
            </button>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if invoices %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>تاريخ الفاتورة</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>المبلغ الإجمالي</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in invoices %}
                            <tr>
                                <td><strong>{{ invoice.invoice_number }}</strong></td>
                                <td>{{ invoice.customer.name }}</td>
                                <td>{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if invoice.due_date %}
                                        {{ invoice.due_date.strftime('%Y-%m-%d') }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td class="currency">{{ invoice.total_amount }}</td>
                                <td>
                                    {% if invoice.status == 'pending' %}
                                        <span class="badge bg-warning">معلقة</span>
                                    {% elif invoice.status == 'paid' %}
                                        <span class="badge bg-success">مدفوعة</span>
                                    {% elif invoice.status == 'overdue' %}
                                        <span class="badge bg-danger">متأخرة</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-info" title="عرض">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-primary" title="طباعة">
                                            <i class="bi bi-printer"></i>
                                        </button>
                                        {% if invoice.status != 'paid' %}
                                        <button class="btn btn-outline-success" title="تسجيل دفعة">
                                            <i class="bi bi-credit-card"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-receipt display-1 text-muted"></i>
                    <h4 class="mt-3">لا توجد فواتير</h4>
                    <p class="text-muted">ابدأ بإنشاء فاتورة جديدة</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
