/* Custom CSS for Car Center Management System */

/* RTL Support */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

/* Statistics Cards */
.card.bg-primary,
.card.bg-success,
.card.bg-warning,
.card.bg-info {
    background: linear-gradient(135deg, var(--bs-primary), var(--bs-primary-dark)) !important;
}

.card.bg-success {
    background: linear-gradient(135deg, var(--bs-success), var(--bs-success-dark)) !important;
}

.card.bg-warning {
    background: linear-gradient(135deg, var(--bs-warning), var(--bs-warning-dark)) !important;
}

.card.bg-info {
    background: linear-gradient(135deg, var(--bs-info), var(--bs-info-dark)) !important;
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Forms */
.form-control,
.form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

/* Tables */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.table thead th {
    background-color: var(--bs-primary);
    color: white;
    font-weight: 600;
    border: none;
}

.table tbody tr {
    transition: background-color 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Badges */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.8rem;
    border-radius: 20px;
}

/* Alerts */
.alert {
    border-radius: 10px;
    border: none;
    font-weight: 500;
}

/* Footer */
footer {
    margin-top: auto;
    border-top: 1px solid #e9ecef;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        font-size: 0.9rem;
    }
}

/* Custom utility classes */
.text-primary-dark {
    color: #0a58ca !important;
}

.bg-light-primary {
    background-color: rgba(13, 110, 253, 0.1) !important;
}

.border-primary-light {
    border-color: rgba(13, 110, 253, 0.3) !important;
}

/* Loading spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Status indicators */
.status-pending {
    color: #ffc107;
}

.status-in-progress {
    color: #0dcaf0;
}

.status-completed {
    color: #198754;
}

.status-cancelled {
    color: #dc3545;
}

/* Print styles */
@media print {
    .navbar,
    .btn,
    footer {
        display: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
}
