{% extends "base.html" %}

{% block title %}{{ title }} - مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="bi bi-tools me-2"></i>
                    {{ title }}
                </h4>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.vehicle_id.label(class="form-label") }}
                            {{ form.vehicle_id(class="form-select", required=true) }}
                            {% if form.vehicle_id.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.vehicle_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.technician_id.label(class="form-label") }}
                            {{ form.technician_id(class="form-select") }}
                            {% if form.technician_id.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.technician_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.service_type.label(class="form-label") }}
                            {{ form.service_type(class="form-select", required=true) }}
                            {% if form.service_type.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.service_type.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.scheduled_date.label(class="form-label") }}
                            {{ form.scheduled_date(class="form-control", type="datetime-local") }}
                            {% if form.scheduled_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.scheduled_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.mileage_at_service.label(class="form-label") }}
                            {{ form.mileage_at_service(class="form-control", min="0") }}
                            {% if form.mileage_at_service.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.mileage_at_service.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control", rows="4", required=true) }}
                        {% if form.description.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.description.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control", rows="3") }}
                        {% if form.notes.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('service_requests') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left me-2"></i>
                            رجوع
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-lg me-2"></i>
                            حفظ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
