{% extends "base.html" %}

{% block title %}إدارة الفنيين - مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-person-workspace me-2"></i>
                إدارة الفنيين
            </h1>
            <a href="{{ url_for('add_technician') }}" class="btn btn-primary">
                <i class="bi bi-person-plus me-2"></i>
                إضافة فني جديد
            </a>
        </div>
    </div>
</div>

<!-- Technicians Cards -->
<div class="row">
    {% if technicians %}
        {% for technician in technicians %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ technician.name }}</h5>
                    {% if technician.is_active %}
                        <span class="badge bg-success">نشط</span>
                    {% else %}
                        <span class="badge bg-secondary">غير نشط</span>
                    {% endif %}
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <i class="bi bi-telephone me-2 text-primary"></i>
                        <strong>الهاتف:</strong> {{ technician.phone or 'غير محدد' }}
                    </div>
                    <div class="mb-2">
                        <i class="bi bi-envelope me-2 text-primary"></i>
                        <strong>البريد:</strong> {{ technician.email or 'غير محدد' }}
                    </div>
                    <div class="mb-2">
                        <i class="bi bi-tools me-2 text-primary"></i>
                        <strong>التخصص:</strong> {{ technician.specialization or 'عام' }}
                    </div>
                    <div class="mb-2">
                        <i class="bi bi-calendar me-2 text-primary"></i>
                        <strong>تاريخ التوظيف:</strong> {{ technician.hire_date.strftime('%Y-%m-%d') if technician.hire_date else 'غير محدد' }}
                    </div>
                    {% if technician.salary %}
                    <div class="mb-2">
                        <i class="bi bi-currency-dollar me-2 text-primary"></i>
                        <strong>الراتب:</strong> {{ "{:,.0f}".format(technician.salary) }} ريال
                    </div>
                    {% endif %}
                    
                    <!-- إحصائيات الفني -->
                    <hr>
                    <div class="row text-center">
                        <div class="col-6">
                            <h6 class="text-primary">{{ technician.service_requests|length }}</h6>
                            <small class="text-muted">طلبات الصيانة</small>
                        </div>
                        <div class="col-6">
                            <h6 class="text-success">
                                {{ technician.service_requests|selectattr('status', 'equalto', 'completed')|list|length }}
                            </h6>
                            <small class="text-muted">مكتملة</small>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="btn-group w-100">
                        <a href="{{ url_for('edit_technician', id=technician.id) }}" 
                           class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-pencil me-1"></i>تعديل
                        </a>
                        <a href="{{ url_for('technician_details', id=technician.id) }}" 
                           class="btn btn-outline-info btn-sm">
                            <i class="bi bi-eye me-1"></i>التفاصيل
                        </a>
                        {% if technician.is_active %}
                        <button class="btn btn-outline-warning btn-sm" 
                                onclick="toggleTechnicianStatus({{ technician.id }}, false)">
                            <i class="bi bi-pause me-1"></i>إيقاف
                        </button>
                        {% else %}
                        <button class="btn btn-outline-success btn-sm" 
                                onclick="toggleTechnicianStatus({{ technician.id }}, true)">
                            <i class="bi bi-play me-1"></i>تفعيل
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="bi bi-person-workspace display-1 text-muted"></i>
                <h4 class="mt-3">لا يوجد فنيين</h4>
                <p class="text-muted">ابدأ بإضافة فني جديد</p>
                <a href="{{ url_for('add_technician') }}" class="btn btn-primary">
                    <i class="bi bi-person-plus me-2"></i>
                    إضافة فني جديد
                </a>
            </div>
        </div>
    {% endif %}
</div>

<!-- Performance Summary -->
{% if technicians %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up me-2"></i>
                    ملخص الأداء
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <h4 class="text-primary">{{ technicians|length }}</h4>
                        <p class="text-muted">إجمالي الفنيين</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-success">{{ technicians|selectattr('is_active', 'equalto', true)|list|length }}</h4>
                        <p class="text-muted">فنيين نشطين</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-info">
                            {{ technicians|sum(attribute='service_requests')|length if technicians else 0 }}
                        </h4>
                        <p class="text-muted">إجمالي المهام</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-warning">
                            {% set avg_salary = technicians|selectattr('salary')|map(attribute='salary')|list %}
                            {% if avg_salary %}
                                {{ "{:,.0f}".format(avg_salary|sum / avg_salary|length) }}
                            {% else %}
                                0
                            {% endif %}
                        </h4>
                        <p class="text-muted">متوسط الراتب</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function toggleTechnicianStatus(technicianId, status) {
    if (confirm('هل أنت متأكد من تغيير حالة الفني؟')) {
        fetch(`/technicians/${technicianId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({status: status})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        });
    }
}
</script>
{% endblock %}
