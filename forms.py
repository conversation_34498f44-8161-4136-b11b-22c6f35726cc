from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, TextAreaField, IntegerField, FloatField, SelectField, DateField, DateTimeField, BooleanField, PasswordField
from wtforms.validators import DataRequired, Email, Length, NumberRange, Optional
from datetime import datetime

class LoginForm(FlaskForm):
    """نموذج تسجيل الدخول"""
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=4, max=20)])
    password = PasswordField('كلمة المرور', validators=[DataRequired()])

class CustomerForm(FlaskForm):
    """نموذج العملاء"""
    name = StringField('اسم العميل', validators=[DataRequired(), Length(max=100)])
    phone = StringField('رقم الهاتف', validators=[DataRequired(), Length(max=20)])
    email = StringField('البريد الإلكتروني', validators=[Optional(), Email(), Length(max=120)])
    address = TextAreaField('العنوان')

class VehicleForm(FlaskForm):
    """نموذج السيارات"""
    customer_id = SelectField('العميل', coerce=int, validators=[DataRequired()])
    make = StringField('الماركة', validators=[DataRequired(), Length(max=50)])
    model = StringField('الموديل', validators=[DataRequired(), Length(max=50)])
    year = IntegerField('سنة الصنع', validators=[DataRequired(), NumberRange(min=1900, max=2030)])
    plate_number = StringField('رقم اللوحة', validators=[DataRequired(), Length(max=20)])
    chassis_number = StringField('رقم الشاسيه', validators=[Optional(), Length(max=50)])
    color = StringField('اللون', validators=[Optional(), Length(max=30)])
    current_mileage = IntegerField('قراءة العداد', validators=[Optional(), NumberRange(min=0)])

class TechnicianForm(FlaskForm):
    """نموذج الفنيين"""
    name = StringField('اسم الفني', validators=[DataRequired(), Length(max=100)])
    phone = StringField('رقم الهاتف', validators=[Optional(), Length(max=20)])
    email = StringField('البريد الإلكتروني', validators=[Optional(), Email(), Length(max=120)])
    specialization = StringField('التخصص', validators=[Optional(), Length(max=100)])
    hire_date = DateField('تاريخ التوظيف', default=datetime.today)
    salary = FloatField('الراتب', validators=[Optional(), NumberRange(min=0)])
    is_active = BooleanField('نشط')

class ServiceForm(FlaskForm):
    """نموذج الخدمات"""
    name = StringField('اسم الخدمة', validators=[DataRequired(), Length(max=100)])
    description = TextAreaField('الوصف')
    price = FloatField('السعر', validators=[DataRequired(), NumberRange(min=0)])
    category = SelectField('الفئة', choices=[
        ('maintenance', 'صيانة دورية'),
        ('repair', 'إصلاح'),
        ('replacement', 'استبدال قطع غيار')
    ])
    estimated_time = IntegerField('الوقت المقدر (دقيقة)', validators=[Optional(), NumberRange(min=0)])
    is_active = BooleanField('نشط', default=True)

class PartForm(FlaskForm):
    """نموذج قطع الغيار"""
    name = StringField('اسم القطعة', validators=[DataRequired(), Length(max=100)])
    part_number = StringField('رقم القطعة', validators=[Optional(), Length(max=50)])
    description = TextAreaField('الوصف')
    category = StringField('الفئة', validators=[Optional(), Length(max=50)])
    unit_price = FloatField('سعر الوحدة', validators=[DataRequired(), NumberRange(min=0)])
    supplier = StringField('المورد', validators=[Optional(), Length(max=100)])

class InventoryForm(FlaskForm):
    """نموذج المخزون"""
    part_id = SelectField('قطعة الغيار', coerce=int, validators=[DataRequired()])
    quantity = IntegerField('الكمية', validators=[DataRequired(), NumberRange(min=0)])
    min_quantity = IntegerField('الحد الأدنى', validators=[DataRequired(), NumberRange(min=0)])

class ServiceRequestForm(FlaskForm):
    """نموذج طلبات الصيانة"""
    vehicle_id = SelectField('السيارة', coerce=int, validators=[DataRequired()])
    technician_id = SelectField('الفني', coerce=int, validators=[Optional()])
    scheduled_date = DateTimeField('تاريخ الموعد', validators=[Optional()])
    service_type = SelectField('نوع الخدمة', choices=[
        ('maintenance', 'صيانة دورية'),
        ('repair', 'إصلاح'),
        ('replacement', 'استبدال قطع غيار')
    ])
    description = TextAreaField('الوصف', validators=[DataRequired()])
    mileage_at_service = IntegerField('قراءة العداد', validators=[Optional(), NumberRange(min=0)])
    notes = TextAreaField('ملاحظات')

class InvoiceForm(FlaskForm):
    """نموذج الفواتير"""
    customer_id = SelectField('العميل', coerce=int, validators=[DataRequired()])
    service_request_id = SelectField('طلب الصيانة', coerce=int, validators=[Optional()])
    due_date = DateField('تاريخ الاستحقاق', validators=[Optional()])
    discount = FloatField('الخصم', validators=[Optional(), NumberRange(min=0)])
    tax_rate = FloatField('معدل الضريبة (%)', validators=[Optional(), NumberRange(min=0, max=100)])

class PaymentForm(FlaskForm):
    """نموذج المدفوعات"""
    invoice_id = SelectField('الفاتورة', coerce=int, validators=[DataRequired()])
    amount = FloatField('المبلغ', validators=[DataRequired(), NumberRange(min=0)])
    payment_method = SelectField('طريقة الدفع', choices=[
        ('cash', 'نقدي'),
        ('card', 'بطاقة'),
        ('bank_transfer', 'تحويل بنكي')
    ])
    reference_number = StringField('رقم المرجع', validators=[Optional(), Length(max=50)])
    notes = TextAreaField('ملاحظات')

class SearchForm(FlaskForm):
    """نموذج البحث"""
    search_term = StringField('البحث', validators=[Optional(), Length(max=100)])
    search_type = SelectField('نوع البحث', choices=[
        ('all', 'الكل'),
        ('name', 'الاسم'),
        ('phone', 'رقم الهاتف'),
        ('plate', 'رقم اللوحة')
    ], default='all')
