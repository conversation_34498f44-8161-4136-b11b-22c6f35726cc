{% extends "base.html" %}

{% block title %}{{ title }} - مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="bi bi-car-front me-2"></i>
                    {{ title }}
                </h4>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.customer_id.label(class="form-label") }}
                            {{ form.customer_id(class="form-select", required=true) }}
                            {% if form.customer_id.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.customer_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.plate_number.label(class="form-label") }}
                            {{ form.plate_number(class="form-control", required=true) }}
                            {% if form.plate_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.plate_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            {{ form.make.label(class="form-label") }}
                            {{ form.make(class="form-control", required=true) }}
                            {% if form.make.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.make.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            {{ form.model.label(class="form-label") }}
                            {{ form.model(class="form-control", required=true) }}
                            {% if form.model.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.model.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            {{ form.year.label(class="form-label") }}
                            {{ form.year(class="form-control", required=true, min="1900", max="2030") }}
                            {% if form.year.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.year.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.chassis_number.label(class="form-label") }}
                            {{ form.chassis_number(class="form-control") }}
                            {% if form.chassis_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.chassis_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            {{ form.color.label(class="form-label") }}
                            {{ form.color(class="form-control") }}
                            {% if form.color.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.color.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            {{ form.current_mileage.label(class="form-label") }}
                            {{ form.current_mileage(class="form-control", min="0") }}
                            {% if form.current_mileage.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.current_mileage.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('vehicles') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left me-2"></i>
                            رجوع
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-lg me-2"></i>
                            حفظ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
