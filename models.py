from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash

db = SQLAlchemy()

class User(UserMixin, db.Model):
    """نموذج المستخدمين"""
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.String(20), default='user')  # admin, user
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Customer(db.Model):
    """نموذج العملاء"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20), nullable=False)
    email = db.Column(db.String(120))
    address = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    vehicles = db.relationship('Vehicle', backref='owner', lazy=True)
    invoices = db.relationship('Invoice', backref='customer', lazy=True)

class Vehicle(db.Model):
    """نموذج السيارات"""
    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    make = db.Column(db.String(50), nullable=False)  # الماركة
    model = db.Column(db.String(50), nullable=False)  # الموديل
    year = db.Column(db.Integer, nullable=False)  # سنة الصنع
    plate_number = db.Column(db.String(20), unique=True, nullable=False)  # رقم اللوحة
    chassis_number = db.Column(db.String(50), unique=True)  # رقم الشاسيه
    color = db.Column(db.String(30))
    current_mileage = db.Column(db.Integer, default=0)  # قراءة العداد الحالية
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    service_requests = db.relationship('ServiceRequest', backref='vehicle', lazy=True)

class Technician(db.Model):
    """نموذج الفنيين"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    specialization = db.Column(db.String(100))  # التخصص
    hire_date = db.Column(db.Date, default=datetime.utcnow().date())
    salary = db.Column(db.Float)
    is_active = db.Column(db.Boolean, default=True)
    
    # العلاقات
    service_requests = db.relationship('ServiceRequest', backref='technician', lazy=True)

class Service(db.Model):
    """نموذج الخدمات"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    price = db.Column(db.Float, nullable=False)
    category = db.Column(db.String(50))  # دورية، إصلاح، استبدال
    estimated_time = db.Column(db.Integer)  # الوقت المقدر بالدقائق
    is_active = db.Column(db.Boolean, default=True)

class Part(db.Model):
    """نموذج قطع الغيار"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    part_number = db.Column(db.String(50), unique=True)
    description = db.Column(db.Text)
    category = db.Column(db.String(50))
    unit_price = db.Column(db.Float, nullable=False)
    supplier = db.Column(db.String(100))
    
    # العلاقات
    inventory = db.relationship('Inventory', backref='part', uselist=False)

class Inventory(db.Model):
    """نموذج المخزون"""
    id = db.Column(db.Integer, primary_key=True)
    part_id = db.Column(db.Integer, db.ForeignKey('part.id'), nullable=False)
    quantity = db.Column(db.Integer, default=0)
    min_quantity = db.Column(db.Integer, default=10)  # الحد الأدنى للتنبيه
    last_updated = db.Column(db.DateTime, default=datetime.utcnow)

class ServiceRequest(db.Model):
    """نموذج طلبات الصيانة"""
    id = db.Column(db.Integer, primary_key=True)
    vehicle_id = db.Column(db.Integer, db.ForeignKey('vehicle.id'), nullable=False)
    technician_id = db.Column(db.Integer, db.ForeignKey('technician.id'))
    request_date = db.Column(db.DateTime, default=datetime.utcnow)
    scheduled_date = db.Column(db.DateTime)
    completion_date = db.Column(db.DateTime)
    status = db.Column(db.String(20), default='pending')  # pending, in_progress, completed, cancelled
    service_type = db.Column(db.String(50))  # دورية، إصلاح، استبدال
    description = db.Column(db.Text)
    mileage_at_service = db.Column(db.Integer)
    total_cost = db.Column(db.Float, default=0)
    notes = db.Column(db.Text)
    
    # العلاقات
    invoice = db.relationship('Invoice', backref='service_request', uselist=False)

class Invoice(db.Model):
    """نموذج الفواتير"""
    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    service_request_id = db.Column(db.Integer, db.ForeignKey('service_request.id'))
    invoice_number = db.Column(db.String(20), unique=True, nullable=False)
    invoice_date = db.Column(db.DateTime, default=datetime.utcnow)
    due_date = db.Column(db.DateTime)
    subtotal = db.Column(db.Float, default=0)
    tax_amount = db.Column(db.Float, default=0)
    discount = db.Column(db.Float, default=0)
    total_amount = db.Column(db.Float, default=0)
    status = db.Column(db.String(20), default='pending')  # pending, paid, overdue
    
    # العلاقات
    details = db.relationship('InvoiceDetail', backref='invoice', lazy=True)
    payments = db.relationship('Payment', backref='invoice', lazy=True)

class InvoiceDetail(db.Model):
    """نموذج تفاصيل الفواتير"""
    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoice.id'), nullable=False)
    item_type = db.Column(db.String(20), nullable=False)  # service, part
    item_id = db.Column(db.Integer, nullable=False)  # معرف الخدمة أو قطعة الغيار
    description = db.Column(db.String(200))
    quantity = db.Column(db.Integer, default=1)
    unit_price = db.Column(db.Float, nullable=False)
    total_price = db.Column(db.Float, nullable=False)

class Payment(db.Model):
    """نموذج المدفوعات"""
    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoice.id'), nullable=False)
    payment_date = db.Column(db.DateTime, default=datetime.utcnow)
    amount = db.Column(db.Float, nullable=False)
    payment_method = db.Column(db.String(20))  # cash, card, bank_transfer
    reference_number = db.Column(db.String(50))
    notes = db.Column(db.Text)
