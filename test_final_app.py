#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار التطبيق النهائي
Test Final Application
"""

try:
    print("🔄 جاري اختبار التطبيق...")
    print("🔄 Testing application...")
    
    import final_app
    print("✅ تم استيراد التطبيق بنجاح")
    print("✅ Application imported successfully")
    
    app = final_app.app
    print("✅ تم الحصول على كائن التطبيق")
    print("✅ Application object obtained")
    
    # اختبار بسيط للتطبيق
    with app.test_client() as client:
        print("🔄 اختبار الصفحة الرئيسية...")
        print("🔄 Testing home page...")
        
        response = client.get('/')
        print(f"📊 كود الاستجابة: {response.status_code}")
        print(f"📊 Response code: {response.status_code}")
        
        if response.status_code in [200, 302]:  # 302 للتوجيه
            print("✅ الصفحة الرئيسية تعمل بشكل صحيح")
            print("✅ Home page works correctly")
        else:
            print("❌ مشكلة في الصفحة الرئيسية")
            print("❌ Issue with home page")
    
    print("\n🎉 التطبيق جاهز للتشغيل!")
    print("🎉 Application is ready to run!")
    print("\n📋 لتشغيل التطبيق:")
    print("📋 To run the application:")
    print("   python run_final_app.py")
    print("   أو / or")
    print("   run_final.bat")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
