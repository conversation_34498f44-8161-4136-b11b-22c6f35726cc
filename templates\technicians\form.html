{% extends "base.html" %}

{% block title %}{{ title }} - مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="bi bi-person-workspace me-2"></i>
                    {{ title }}
                </h4>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.name.label(class="form-label") }}
                            {{ form.name(class="form-control", required=true) }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.phone.label(class="form-label") }}
                            {{ form.phone(class="form-control") }}
                            {% if form.phone.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.phone.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.email.label(class="form-label") }}
                            {{ form.email(class="form-control", type="email") }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.specialization.label(class="form-label") }}
                            {{ form.specialization(class="form-control") }}
                            {% if form.specialization.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.specialization.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.hire_date.label(class="form-label") }}
                            {{ form.hire_date(class="form-control") }}
                            {% if form.hire_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.hire_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.salary.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.salary(class="form-control", min="0", step="0.01") }}
                                <span class="input-group-text">ريال</span>
                            </div>
                            {% if form.salary.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.salary.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_active(class="form-check-input") }}
                            {{ form.is_active.label(class="form-check-label") }}
                        </div>
                        {% if form.is_active.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.is_active.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('technicians') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left me-2"></i>
                            رجوع
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-lg me-2"></i>
                            حفظ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
