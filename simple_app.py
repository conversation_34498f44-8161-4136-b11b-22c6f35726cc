from flask import Flask, render_template, redirect, url_for, flash
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
import os

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'car-center-secret-key-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///car_center.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# Simple User model
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def is_authenticated(self):
        return True
    
    def is_active(self):
        return True
    
    def is_anonymous(self):
        return False
    
    def get_id(self):
        return str(self.id)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Routes
@app.route('/')
def index():
    return render_template('dashboard.html', stats={
        'total_customers': 0,
        'total_vehicles': 0,
        'pending_requests': 0,
        'monthly_revenue': 0
    }, recent_requests=[], low_stock_items=[])

@app.route('/login')
def login():
    return render_template('login.html', form=None)

@app.route('/customers')
@login_required
def customers():
    return render_template('customers/list.html', customers={'items': []})

@app.route('/vehicles')
@login_required
def vehicles():
    return render_template('vehicles/list.html', vehicles={'items': []})

@app.route('/service-requests')
@login_required
def service_requests():
    return render_template('service_requests/list.html', requests={'items': []})

@app.route('/services')
@login_required
def services():
    return render_template('services/list.html', services=[])

@app.route('/parts')
@login_required
def parts():
    return render_template('parts/list.html', parts=[])

@app.route('/inventory')
@login_required
def inventory():
    return render_template('inventory/list.html', inventory_items=[])

@app.route('/invoices')
@login_required
def invoices():
    return render_template('invoices/list.html', invoices=[])

@app.route('/payments')
@login_required
def payments():
    return render_template('payments/list.html', payments=[])

@app.route('/reports')
@login_required
def reports():
    return render_template('reports/index.html')

@app.route('/dashboard')
@login_required
def dashboard():
    return render_template('dashboard.html', stats={
        'total_customers': 0,
        'total_vehicles': 0,
        'pending_requests': 0,
        'monthly_revenue': 0
    }, recent_requests=[], low_stock_items=[])

@app.route('/add_customer')
@login_required
def add_customer():
    return "إضافة عميل جديد - قيد التطوير"

@app.route('/add_vehicle')
@login_required
def add_vehicle():
    return "إضافة سيارة جديدة - قيد التطوير"

@app.route('/add_service_request')
@login_required
def add_service_request():
    return "طلب صيانة جديد - قيد التطوير"

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('index'))

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        # Create default user
        if not User.query.filter_by(username='admin').first():
            admin = User(username='admin')
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print("تم إنشاء المستخدم: admin / admin123")
    
    print("بدء تشغيل الخادم على http://127.0.0.1:5000")
    app.run(debug=True, host='127.0.0.1', port=5000)
