from flask import Flask, render_template, redirect, url_for, flash, request
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import os

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'car-center-secret-key-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///car_center.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# Simple User model
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def is_authenticated(self):
        return True
    
    def is_active(self):
        return True
    
    def is_anonymous(self):
        return False
    
    def get_id(self):
        return str(self.id)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Routes
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        if username and password:
            user = User.query.filter_by(username=username).first()
            if user and user.check_password(password):
                login_user(user)
                flash('تم تسجيل الدخول بنجاح!', 'success')
                next_page = request.args.get('next')
                return redirect(next_page) if next_page else redirect(url_for('dashboard'))
            else:
                flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
        else:
            flash('يرجى إدخال اسم المستخدم وكلمة المرور', 'error')

    return render_template('login_simple.html')

@app.route('/customers')
@login_required
def customers():
    return render_template('customers/list.html', customers={'items': []})

@app.route('/vehicles')
@login_required
def vehicles():
    return render_template('vehicles/list.html', vehicles={'items': []})

@app.route('/service-requests')
@login_required
def service_requests():
    return render_template('service_requests/list.html', requests={'items': []})

@app.route('/services')
@login_required
def services():
    return render_template('services/list.html', services=[])

@app.route('/parts')
@login_required
def parts():
    return render_template('parts/list.html', parts=[])

@app.route('/inventory')
@login_required
def inventory():
    return render_template('inventory/list.html', inventory_items=[])

@app.route('/invoices')
@login_required
def invoices():
    return render_template('invoices/list.html', invoices=[])

@app.route('/payments')
@login_required
def payments():
    return render_template('payments/list.html', payments=[])

@app.route('/reports')
@login_required
def reports():
    return render_template('reports/index.html')

@app.route('/dashboard')
@login_required
def dashboard():
    # بيانات تجريبية لعرض لوحة التحكم
    stats = {
        'total_customers': 45,
        'total_vehicles': 67,
        'pending_requests': 8,
        'monthly_revenue': 25000
    }

    # طلبات صيانة تجريبية
    class MockRequest:
        def __init__(self, customer_name, car_make, car_model, date, status):
            self.vehicle = MockVehicle(customer_name, car_make, car_model)
            self.request_date = datetime.strptime(date, '%Y-%m-%d')
            self.status = status

    class MockVehicle:
        def __init__(self, owner_name, make, model):
            self.owner = MockOwner(owner_name)
            self.make = make
            self.model = model

    class MockOwner:
        def __init__(self, name):
            self.name = name

    recent_requests = [
        MockRequest("أحمد محمد", "تويوتا", "كامري", "2024-01-15", "pending"),
        MockRequest("سعد العتيبي", "هوندا", "أكورد", "2024-01-14", "in_progress"),
        MockRequest("محمد الغامدي", "نيسان", "التيما", "2024-01-13", "completed"),
        MockRequest("خالد القحطاني", "هيونداي", "إلنترا", "2024-01-12", "pending"),
        MockRequest("فهد الشمري", "كيا", "أوبتيما", "2024-01-11", "completed"),
    ]

    # عناصر مخزون منخفضة تجريبية
    class MockInventoryItem:
        def __init__(self, part_name, part_number, quantity, min_quantity):
            self.part = MockPart(part_name, part_number)
            self.quantity = quantity
            self.min_quantity = min_quantity

    class MockPart:
        def __init__(self, name, part_number):
            self.name = name
            self.part_number = part_number

    low_stock_items = [
        MockInventoryItem("زيت محرك", "OIL001", 5, 10),
        MockInventoryItem("فلتر هواء", "AIR001", 3, 15),
        MockInventoryItem("فحمات فرامل", "BRK001", 2, 8),
    ]

    return render_template('dashboard.html',
                         stats=stats,
                         recent_requests=recent_requests,
                         low_stock_items=low_stock_items)

@app.route('/add_customer')
@login_required
def add_customer():
    return "إضافة عميل جديد - قيد التطوير"

@app.route('/add_vehicle')
@login_required
def add_vehicle():
    return "إضافة سيارة جديدة - قيد التطوير"

@app.route('/add_service_request')
@login_required
def add_service_request():
    return "طلب صيانة جديد - قيد التطوير"

@app.route('/technicians')
@login_required
def technicians():
    return "إدارة الفنيين - قيد التطوير"

@app.route('/add_invoice')
@login_required
def add_invoice():
    return "إنشاء فاتورة جديدة - قيد التطوير"

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        # Create default user
        if not User.query.filter_by(username='admin').first():
            admin = User(username='admin')
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print("تم إنشاء المستخدم: admin / admin123")
    
    print("بدء تشغيل الخادم على http://127.0.0.1:5000")
    app.run(debug=True, host='127.0.0.1', port=5000)
