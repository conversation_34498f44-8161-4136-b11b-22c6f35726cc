{% extends "customers_base.html" %}

{% block title %}{{ title }} - مركز صيانة السيارات{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="mb-1">
                    <i class="bi bi-person-plus me-2 text-primary"></i>
                    {{ title }}
                </h1>
                <p class="text-muted mb-0">إضافة عميل جديد إلى النظام</p>
            </div>
            <div>
                <a href="{{ url_for('customers') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-person-badge me-2"></i>
                    بيانات العميل الأساسية
                </h5>
            </div>
            <div class="card-body p-4">
                <form method="POST" class="needs-validation" novalidate id="customerForm">
                    {{ form.hidden_tag() }}

                    <!-- Personal Information Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">
                                <i class="bi bi-person-circle me-2"></i>
                                المعلومات الشخصية
                            </h6>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">
                                <i class="bi bi-person me-1"></i>
                                اسم العميل <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-person"></i>
                                </span>
                                {{ form.name(class="form-control", required=true, placeholder="أدخل الاسم الكامل") }}
                            </div>
                            <div class="form-text">أدخل الاسم الكامل للعميل</div>
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">
                                <i class="bi bi-telephone me-1"></i>
                                رقم الهاتف <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-telephone"></i>
                                </span>
                                {{ form.phone(class="form-control", required=true, placeholder="05xxxxxxxx", pattern="[0-9]{10}") }}
                            </div>
                            <div class="form-text">رقم الهاتف المحمول (10 أرقام)</div>
                            {% if form.phone.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.phone.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Contact Information Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">
                                <i class="bi bi-envelope me-2"></i>
                                معلومات الاتصال
                            </h6>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                <i class="bi bi-envelope me-1"></i>
                                البريد الإلكتروني
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-envelope"></i>
                                </span>
                                {{ form.email(class="form-control", type="email", placeholder="<EMAIL>") }}
                            </div>
                            <div class="form-text">البريد الإلكتروني (اختياري)</div>
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="city" class="form-label">
                                <i class="bi bi-geo-alt me-1"></i>
                                المدينة
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-geo-alt"></i>
                                </span>
                                <select class="form-select" name="city" id="city">
                                    <option value="">اختر المدينة</option>
                                    <option value="الرياض">الرياض</option>
                                    <option value="جدة">جدة</option>
                                    <option value="الدمام">الدمام</option>
                                    <option value="مكة المكرمة">مكة المكرمة</option>
                                    <option value="المدينة المنورة">المدينة المنورة</option>
                                    <option value="الطائف">الطائف</option>
                                    <option value="أبها">أبها</option>
                                    <option value="حائل">حائل</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                            <div class="form-text">اختر مدينة العميل</div>
                        </div>
                    </div>

                    <!-- Address Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">
                                <i class="bi bi-house me-2"></i>
                                العنوان التفصيلي
                            </h6>
                        </div>

                        <div class="col-12 mb-3">
                            <label for="address" class="form-label">
                                <i class="bi bi-house me-1"></i>
                                العنوان الكامل
                            </label>
                            {{ form.address(class="form-control", rows="3", placeholder="أدخل العنوان التفصيلي (الحي، الشارع، رقم المبنى)") }}
                            <div class="form-text">العنوان التفصيلي للعميل (اختياري)</div>
                            {% if form.address.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.address.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Additional Information Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">
                                <i class="bi bi-info-circle me-2"></i>
                                معلومات إضافية
                            </h6>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="customer_type" class="form-label">
                                <i class="bi bi-star me-1"></i>
                                نوع العميل
                            </label>
                            <select class="form-select" name="customer_type" id="customer_type">
                                <option value="عادي">عميل عادي</option>
                                <option value="مميز">عميل مميز</option>
                                <option value="VIP">عميل VIP</option>
                            </select>
                            <div class="form-text">تصنيف العميل في النظام</div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="notes" class="form-label">
                                <i class="bi bi-chat-text me-1"></i>
                                ملاحظات
                            </label>
                            <textarea class="form-control" name="notes" id="notes" rows="2" placeholder="أي ملاحظات خاصة بالعميل"></textarea>
                            <div class="form-text">ملاحظات إضافية (اختياري)</div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center pt-3 border-top">
                                <div class="text-muted">
                                    <small>
                                        <i class="bi bi-info-circle me-1"></i>
                                        الحقول المميزة بـ <span class="text-danger">*</span> مطلوبة
                                    </small>
                                </div>
                                <div>
                                    <a href="{{ url_for('customers') }}" class="btn btn-outline-secondary me-2">
                                        <i class="bi bi-x-circle me-2"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-2"></i>
                                        حفظ العميل
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Help Card -->
        <div class="card mt-4 border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="bi bi-lightbulb me-2"></i>
                    نصائح مفيدة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-info">معلومات مطلوبة:</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-check-circle text-success me-2"></i>اسم العميل الكامل</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>رقم الهاتف المحمول</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-info">معلومات اختيارية:</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-info-circle text-info me-2"></i>البريد الإلكتروني</li>
                            <li><i class="bi bi-info-circle text-info me-2"></i>العنوان التفصيلي</li>
                            <li><i class="bi bi-info-circle text-info me-2"></i>المدينة والملاحظات</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Form validation and enhancement
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('customerForm');
    const nameInput = document.querySelector('input[name="name"]');
    const phoneInput = document.querySelector('input[name="phone"]');
    const emailInput = document.querySelector('input[name="email"]');

    // Phone number formatting
    phoneInput.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length > 10) {
            value = value.substring(0, 10);
        }
        e.target.value = value;

        // Validation feedback
        if (value.length === 10 && value.startsWith('05')) {
            e.target.classList.remove('is-invalid');
            e.target.classList.add('is-valid');
        } else if (value.length > 0) {
            e.target.classList.remove('is-valid');
            e.target.classList.add('is-invalid');
        } else {
            e.target.classList.remove('is-valid', 'is-invalid');
        }
    });

    // Name validation
    nameInput.addEventListener('input', function(e) {
        const value = e.target.value.trim();
        if (value.length >= 3) {
            e.target.classList.remove('is-invalid');
            e.target.classList.add('is-valid');
        } else if (value.length > 0) {
            e.target.classList.remove('is-valid');
            e.target.classList.add('is-invalid');
        } else {
            e.target.classList.remove('is-valid', 'is-invalid');
        }
    });

    // Email validation
    emailInput.addEventListener('input', function(e) {
        const value = e.target.value.trim();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        if (value === '') {
            e.target.classList.remove('is-valid', 'is-invalid');
        } else if (emailRegex.test(value)) {
            e.target.classList.remove('is-invalid');
            e.target.classList.add('is-valid');
        } else {
            e.target.classList.remove('is-valid');
            e.target.classList.add('is-invalid');
        }
    });

    // Form submission validation
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        let isValid = true;
        const name = nameInput.value.trim();
        const phone = phoneInput.value.trim();
        const email = emailInput.value.trim();

        // Reset previous validation
        document.querySelectorAll('.is-invalid').forEach(el => {
            el.classList.remove('is-invalid');
        });

        // Validate name
        if (name.length < 3) {
            nameInput.classList.add('is-invalid');
            showError(nameInput, 'يجب أن يكون الاسم 3 أحرف على الأقل');
            isValid = false;
        }

        // Validate phone
        if (phone.length !== 10 || !phone.startsWith('05')) {
            phoneInput.classList.add('is-invalid');
            showError(phoneInput, 'رقم الهاتف يجب أن يكون 10 أرقام ويبدأ بـ 05');
            isValid = false;
        }

        // Validate email if provided
        if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
            emailInput.classList.add('is-invalid');
            showError(emailInput, 'البريد الإلكتروني غير صحيح');
            isValid = false;
        }

        if (isValid) {
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الحفظ...';
            submitBtn.disabled = true;

            // Submit form
            setTimeout(() => {
                form.submit();
            }, 500);
        } else {
            // Scroll to first error
            const firstError = document.querySelector('.is-invalid');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstError.focus();
            }
        }
    });

    function showError(input, message) {
        let errorDiv = input.parentNode.parentNode.querySelector('.invalid-feedback');
        if (!errorDiv) {
            errorDiv = document.createElement('div');
            errorDiv.className = 'invalid-feedback d-block';
            input.parentNode.parentNode.appendChild(errorDiv);
        }
        errorDiv.textContent = message;
    }

    // Auto-capitalize name
    nameInput.addEventListener('input', function(e) {
        const words = e.target.value.split(' ');
        const capitalizedWords = words.map(word => {
            if (word.length > 0) {
                return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
            }
            return word;
        });
        e.target.value = capitalizedWords.join(' ');
    });

    // Success animation for valid inputs
    document.querySelectorAll('.form-control, .form-select').forEach(input => {
        input.addEventListener('focus', function() {
            this.parentNode.style.transform = 'scale(1.02)';
            this.parentNode.style.transition = 'transform 0.2s ease';
        });

        input.addEventListener('blur', function() {
            this.parentNode.style.transform = 'scale(1)';
        });
    });
});

// Show success message after form submission
function showSuccessMessage() {
    const alert = document.createElement('div');
    alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alert.style.top = '20px';
    alert.style.right = '20px';
    alert.style.zIndex = '9999';
    alert.innerHTML = `
        <i class="bi bi-check-circle me-2"></i>
        تم حفظ بيانات العميل بنجاح!
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alert);

    setTimeout(() => {
        alert.remove();
    }, 5000);
}
</script>
{% endblock %}
