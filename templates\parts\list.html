{% extends "base.html" %}

{% block title %}قطع الغيار - مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-box me-2"></i>
                قطع الغيار
            </h1>
            <button class="btn btn-primary">
                <i class="bi bi-plus-lg me-2"></i>
                إضافة قطعة غيار جديدة
            </button>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if parts %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>اسم القطعة</th>
                                <th>رقم القطعة</th>
                                <th>الفئة</th>
                                <th>سعر الوحدة</th>
                                <th>المورد</th>
                                <th>الكمية المتاحة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for part in parts %}
                            <tr>
                                <td>
                                    <strong>{{ part.name }}</strong>
                                    {% if part.description %}
                                    <br><small class="text-muted">{{ part.description[:50] }}...</small>
                                    {% endif %}
                                </td>
                                <td>{{ part.part_number or '-' }}</td>
                                <td>{{ part.category or '-' }}</td>
                                <td class="currency">{{ part.unit_price }}</td>
                                <td>{{ part.supplier or '-' }}</td>
                                <td>
                                    {% if part.inventory %}
                                        {% if part.inventory.quantity <= part.inventory.min_quantity %}
                                            <span class="badge bg-danger">{{ part.inventory.quantity }}</span>
                                        {% else %}
                                            <span class="badge bg-success">{{ part.inventory.quantity }}</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="badge bg-secondary">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-outline-info" title="إدارة المخزون">
                                            <i class="bi bi-box-seam"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-box display-1 text-muted"></i>
                    <h4 class="mt-3">لا توجد قطع غيار</h4>
                    <p class="text-muted">ابدأ بإضافة قطع غيار جديدة</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
