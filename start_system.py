#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تشغيل شامل لنظام إدارة مركز صيانة السيارات
"""

import os
import sys
from datetime import datetime

def print_header():
    """طباعة رأس النظام"""
    print("=" * 60)
    print("🚗 نظام إدارة مركز صيانة السيارات 🚗")
    print("=" * 60)
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🔧 مطور بلغة Python مع Flask و Bootstrap")
    print("=" * 60)

def check_requirements():
    """فحص المتطلبات"""
    print("\n🔍 فحص المتطلبات...")
    
    required_modules = [
        'flask', 'flask_sqlalchemy', 'flask_wtf', 
        'flask_login', 'wtforms', 'email_validator'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"   ✓ {module}")
        except ImportError:
            missing_modules.append(module)
            print(f"   ✗ {module} - غير مثبت")
    
    if missing_modules:
        print(f"\n❌ المكتبات المفقودة: {', '.join(missing_modules)}")
        print("💡 قم بتثبيتها باستخدام: pip install -r requirements.txt")
        return False
    
    print("✅ جميع المتطلبات متوفرة!")
    return True

def setup_database():
    """إعداد قاعدة البيانات"""
    print("\n🗄️ إعداد قاعدة البيانات...")
    
    try:
        from app import app
        from models import db, User
        
        with app.app_context():
            # إنشاء الجداول
            db.create_all()
            print("   ✓ تم إنشاء جداول قاعدة البيانات")
            
            # إنشاء المستخدم الافتراضي
            if not User.query.filter_by(username='admin').first():
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    role='admin'
                )
                admin.set_password('admin123')
                db.session.add(admin)
                db.session.commit()
                print("   ✓ تم إنشاء المستخدم الافتراضي")
            else:
                print("   ✓ المستخدم الافتراضي موجود")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def add_sample_data_option():
    """خيار إضافة بيانات تجريبية"""
    print("\n📊 هل تريد إضافة بيانات تجريبية؟")
    choice = input("اكتب 'نعم' أو 'y' لإضافة البيانات التجريبية: ").lower()
    
    if choice in ['نعم', 'y', 'yes']:
        try:
            from add_sample_data import add_sample_data
            add_sample_data()
            return True
        except Exception as e:
            print(f"❌ خطأ في إضافة البيانات التجريبية: {e}")
            return False
    else:
        print("⏭️ تم تخطي إضافة البيانات التجريبية")
        return True

def start_server():
    """بدء تشغيل الخادم"""
    print("\n🚀 بدء تشغيل الخادم...")
    
    try:
        from app import app
        
        print("📍 الخادم يعمل على: http://127.0.0.1:5000")
        print("👤 اسم المستخدم: admin")
        print("🔑 كلمة المرور: admin123")
        print("\n🌐 افتح المتصفح وانتقل إلى الرابط أعلاه")
        print("⏹️ اضغط Ctrl+C لإيقاف الخادم")
        print("=" * 60)
        
        # تشغيل التطبيق
        app.run(debug=True, host='127.0.0.1', port=5000)
        
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # فحص المتطلبات
    if not check_requirements():
        sys.exit(1)
    
    # إعداد قاعدة البيانات
    if not setup_database():
        sys.exit(1)
    
    # خيار إضافة بيانات تجريبية
    add_sample_data_option()
    
    # بدء تشغيل الخادم
    start_server()

if __name__ == "__main__":
    main()
