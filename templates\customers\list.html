{% extends "base.html" %}

{% block title %}قائمة العملاء - مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-people me-2"></i>
                قائمة العملاء
            </h1>
            <a href="{{ url_for('add_customer') }}" class="btn btn-primary">
                <i class="bi bi-person-plus me-2"></i>
                إضافة عميل جديد
            </a>
        </div>
    </div>
</div>

<!-- Search Form -->
<div class="row mb-4">
    <div class="col-md-6">
        <form method="GET" class="d-flex">
            <input type="text" name="search_term" class="form-control me-2" 
                   placeholder="البحث عن عميل..." value="{{ request.args.get('search_term', '') }}">
            <button type="submit" class="btn btn-outline-primary">
                <i class="bi bi-search"></i>
            </button>
        </form>
    </div>
</div>

<!-- Customers Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if customers.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>اسم العميل</th>
                                <th>رقم الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>عدد السيارات</th>
                                <th>تاريخ التسجيل</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for customer in customers.items %}
                            <tr>
                                <td>{{ customer.id }}</td>
                                <td>
                                    <strong>{{ customer.name }}</strong>
                                    {% if customer.address %}
                                    <br><small class="text-muted">{{ customer.address[:50] }}...</small>
                                    {% endif %}
                                </td>
                                <td>{{ customer.phone }}</td>
                                <td>{{ customer.email or '-' }}</td>
                                <td>
                                    <span class="badge bg-info">{{ customer.vehicles|length }}</span>
                                </td>
                                <td>{{ customer.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('edit_customer', id=customer.id) }}" 
                                           class="btn btn-outline-primary" title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger btn-delete" 
                                                onclick="deleteCustomer({{ customer.id }})" title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if customers.pages > 1 %}
                <nav aria-label="صفحات العملاء">
                    <ul class="pagination justify-content-center">
                        {% if customers.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('customers', page=customers.prev_num) }}">السابق</a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in customers.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != customers.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('customers', page=page_num) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if customers.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('customers', page=customers.next_num) }}">التالي</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-people display-1 text-muted"></i>
                    <h4 class="mt-3">لا توجد عملاء</h4>
                    <p class="text-muted">ابدأ بإضافة عميل جديد</p>
                    <a href="{{ url_for('add_customer') }}" class="btn btn-primary">
                        <i class="bi bi-person-plus me-2"></i>
                        إضافة عميل جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف هذا العميل؟ سيتم حذف جميع البيانات المرتبطة به.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function deleteCustomer(customerId) {
    var deleteForm = document.getElementById('deleteForm');
    deleteForm.action = '/customers/' + customerId + '/delete';
    var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
{% endblock %}
