#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إدارة مركز صيانة السيارات - التطبيق الشامل
Car Center Management System - Complete Application
"""

from flask import Flask, render_template, redirect, url_for, flash, request, session
from datetime import datetime
import webbrowser
import threading
import time

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'car-center-secret-key-2024'

# Simple authentication
def is_logged_in():
    return session.get('logged_in', False)

def require_login():
    if not is_logged_in():
        flash('يرجى تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))
    return None

# Auto-open browser
def open_browser():
    time.sleep(1.5)
    webbrowser.open('http://127.0.0.1:5000')

# Routes
@app.route('/')
def index():
    if is_logged_in():
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if is_logged_in():
        return redirect(url_for('dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if username == 'admin' and password == 'admin123':
            session['logged_in'] = True
            session['username'] = username
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('login_simple.html')

@app.route('/dashboard')
def dashboard():
    auth_check = require_login()
    if auth_check:
        return auth_check
    
    # بيانات تجريبية لعرض لوحة التحكم
    stats = {
        'total_customers': 45,
        'total_vehicles': 67,
        'pending_requests': 8,
        'monthly_revenue': 25000
    }
    
    # طلبات صيانة تجريبية
    class MockRequest:
        def __init__(self, customer_name, car_make, car_model, date, status):
            self.vehicle = MockVehicle(customer_name, car_make, car_model)
            self.request_date = datetime.strptime(date, '%Y-%m-%d')
            self.status = status
    
    class MockVehicle:
        def __init__(self, owner_name, make, model):
            self.owner = MockOwner(owner_name)
            self.make = make
            self.model = model
    
    class MockOwner:
        def __init__(self, name):
            self.name = name
    
    recent_requests = [
        MockRequest("أحمد محمد", "تويوتا", "كامري", "2024-01-15", "pending"),
        MockRequest("سعد العتيبي", "هوندا", "أكورد", "2024-01-14", "in_progress"),
        MockRequest("محمد الغامدي", "نيسان", "التيما", "2024-01-13", "completed"),
        MockRequest("خالد القحطاني", "هيونداي", "إلنترا", "2024-01-12", "pending"),
        MockRequest("فهد الشمري", "كيا", "أوبتيما", "2024-01-11", "completed"),
    ]
    
    # عناصر مخزون منخفضة تجريبية
    class MockInventoryItem:
        def __init__(self, part_name, part_number, quantity, min_quantity):
            self.part = MockPart(part_name, part_number)
            self.quantity = quantity
            self.min_quantity = min_quantity
    
    class MockPart:
        def __init__(self, name, part_number):
            self.name = name
            self.part_number = part_number
    
    low_stock_items = [
        MockInventoryItem("زيت محرك", "OIL001", 5, 10),
        MockInventoryItem("فلتر هواء", "AIR001", 3, 15),
        MockInventoryItem("فحمات فرامل", "BRK001", 2, 8),
    ]
    
    return render_template('dashboard_simple.html',
                         stats=stats,
                         recent_requests=recent_requests,
                         low_stock_items=low_stock_items)

@app.route('/customers')
def customers():
    auth_check = require_login()
    if auth_check:
        return auth_check
    
    # بيانات عملاء تجريبية
    class MockCustomer:
        def __init__(self, id, name, phone, email, address, created_at, vehicle_count=0):
            self.id = id
            self.name = name
            self.phone = phone
            self.email = email
            self.address = address
            self.created_at = datetime.strptime(created_at, '%Y-%m-%d')
            self.vehicles = [f"vehicle_{i}" for i in range(vehicle_count)]
    
    # بيانات تجريبية للعملاء
    mock_customers = [
        MockCustomer(1, "أحمد محمد العلي", "0501234567", "<EMAIL>", "الرياض، حي النخيل، شارع الملك فهد", "2024-01-15", 2),
        MockCustomer(2, "سعد العتيبي", "0509876543", "<EMAIL>", "جدة، حي الصفا، طريق الملك عبدالعزيز", "2024-01-10", 1),
        MockCustomer(3, "محمد الغامدي", "0551234567", "<EMAIL>", "الدمام، حي الفيصلية، شارع الأمير محمد", "2024-01-08", 3),
        MockCustomer(4, "خالد القحطاني", "0561234567", "<EMAIL>", "مكة المكرمة، حي العزيزية، طريق الحرم", "2024-01-05", 1),
        MockCustomer(5, "فهد الشمري", "0571234567", "<EMAIL>", "المدينة المنورة، حي قباء، شارع النور", "2024-01-03", 2),
        MockCustomer(6, "عبدالله الدوسري", "0581234567", "<EMAIL>", "الطائف، حي الشفا، طريق الهدا", "2023-12-28", 1),
        MockCustomer(7, "ناصر الحربي", "0591234567", "<EMAIL>", "أبها، حي المنهل، شارع الملك خالد", "2023-12-25", 2),
        MockCustomer(8, "راشد المطيري", "0501111111", "<EMAIL>", "حائل، حي الصناعية، طريق الملك عبدالعزيز", "2023-12-20", 1),
    ]
    
    # محاكاة البحث
    search_term = request.args.get('search_term', '')
    if search_term:
        mock_customers = [c for c in mock_customers if search_term.lower() in c.name.lower() or search_term in c.phone]
    
    # محاكاة التصفح
    class MockPagination:
        def __init__(self, items, page=1, per_page=10):
            self.items = items
            self.page = page
            self.per_page = per_page
            self.total = len(items)
            self.pages = (self.total + per_page - 1) // per_page
            self.has_prev = page > 1
            self.has_next = page < self.pages
            self.prev_num = page - 1 if self.has_prev else None
            self.next_num = page + 1 if self.has_next else None
        
        def iter_pages(self):
            for i in range(1, self.pages + 1):
                yield i
    
    page = request.args.get('page', 1, type=int)
    customers_paginated = MockPagination(mock_customers, page, 5)
    
    return render_template('customers/list.html', customers=customers_paginated)

@app.route('/add_customer', methods=['GET', 'POST'])
def add_customer():
    auth_check = require_login()
    if auth_check:
        return auth_check
    
    if request.method == 'POST':
        name = request.form.get('name')
        phone = request.form.get('phone')
        email = request.form.get('email')
        address = request.form.get('address')
        city = request.form.get('city')
        customer_type = request.form.get('customer_type')
        notes = request.form.get('notes')

        # التحقق من صحة البيانات
        errors = []

        if not name or len(name.strip()) < 3:
            errors.append('اسم العميل مطلوب ويجب أن يكون 3 أحرف على الأقل')

        if not phone or len(phone) != 10 or not phone.startswith('05'):
            errors.append('رقم الهاتف يجب أن يكون 10 أرقام ويبدأ بـ 05')

        if email and '@' not in email:
            errors.append('البريد الإلكتروني غير صحيح')

        if errors:
            for error in errors:
                flash(error, 'error')
        else:
            # في التطبيق الحقيقي، سيتم حفظ جميع البيانات في قاعدة البيانات
            success_msg = f'تم إضافة العميل {name} بنجاح!'
            if city:
                success_msg += f' من {city}'
            if customer_type and customer_type != 'عادي':
                success_msg += f' كعميل {customer_type}'

            flash(success_msg, 'success')
            return redirect(url_for('customers'))
    
    # نموذج وهمي
    class MockForm:
        def __init__(self):
            self.name = MockField('اسم العميل', 'name')
            self.phone = MockField('رقم الهاتف', 'phone')
            self.email = MockField('البريد الإلكتروني', 'email')
            self.address = MockField('العنوان', 'address')
        
        def hidden_tag(self):
            return ''
    
    class MockField:
        def __init__(self, label_text, name):
            self.label_text = label_text
            self.name = name
            self.errors = []
        
        def label(self, **kwargs):
            return f'<label for="{self.name}" class="{kwargs.get("class", "")}">{self.label_text}</label>'
        
        def __call__(self, **kwargs):
            input_type = kwargs.get('type', 'text')
            css_class = kwargs.get('class', '')
            required = 'required' if kwargs.get('required') else ''
            rows = f'rows="{kwargs.get("rows")}"' if kwargs.get('rows') else ''
            
            if self.name == 'address':
                return f'<textarea name="{self.name}" class="{css_class}" {rows} placeholder="{self.label_text}"></textarea>'
            else:
                return f'<input type="{input_type}" name="{self.name}" class="{css_class}" {required} placeholder="{self.label_text}">'
    
    form = MockForm()
    return render_template('customers/form_simple.html', form=form, title='إضافة عميل جديد')

@app.route('/customers/<int:customer_id>/edit', methods=['GET', 'POST'])
def edit_customer(customer_id):
    auth_check = require_login()
    if auth_check:
        return auth_check
    
    if request.method == 'POST':
        name = request.form.get('name')
        phone = request.form.get('phone')
        email = request.form.get('email')
        address = request.form.get('address')
        
        if not name or not phone:
            flash('الاسم ورقم الهاتف مطلوبان', 'error')
        else:
            flash(f'تم تحديث بيانات العميل بنجاح!', 'success')
            return redirect(url_for('customers'))
    
    # نموذج وهمي مع بيانات افتراضية
    class MockForm:
        def __init__(self):
            self.name = MockField('اسم العميل', 'name')
            self.phone = MockField('رقم الهاتف', 'phone')
            self.email = MockField('البريد الإلكتروني', 'email')
            self.address = MockField('العنوان', 'address')
        
        def hidden_tag(self):
            return ''
    
    class MockField:
        def __init__(self, label_text, name):
            self.label_text = label_text
            self.name = name
            self.errors = []
        
        def label(self, **kwargs):
            return f'<label for="{self.name}" class="{kwargs.get("class", "")}">{self.label_text}</label>'
        
        def __call__(self, **kwargs):
            input_type = kwargs.get('type', 'text')
            css_class = kwargs.get('class', '')
            required = 'required' if kwargs.get('required') else ''
            rows = f'rows="{kwargs.get("rows")}"' if kwargs.get('rows') else ''
            
            # قيم افتراضية للتعديل
            default_values = {
                'name': 'أحمد محمد العلي',
                'phone': '0501234567',
                'email': '<EMAIL>',
                'address': 'الرياض، حي النخيل، شارع الملك فهد'
            }
            
            value = f'value="{default_values.get(self.name, "")}"'
            
            if self.name == 'address':
                return f'<textarea name="{self.name}" class="{css_class}" {rows} placeholder="{self.label_text}">{default_values.get(self.name, "")}</textarea>'
            else:
                return f'<input type="{input_type}" name="{self.name}" class="{css_class}" {required} {value} placeholder="{self.label_text}">'
    
    form = MockForm()
    return render_template('customers/form_simple.html', form=form, title='تعديل بيانات العميل')

@app.route('/customers/<int:customer_id>/delete', methods=['POST'])
def delete_customer(customer_id):
    auth_check = require_login()
    if auth_check:
        return auth_check
    
    flash('تم حذف العميل بنجاح', 'success')
    return redirect(url_for('customers'))

# Navigation routes
@app.route('/vehicles')
def vehicles():
    auth_check = require_login()
    if auth_check:
        return auth_check
    flash('صفحة السيارات قيد التطوير', 'info')
    return redirect(url_for('dashboard'))

@app.route('/service_requests')
def service_requests():
    auth_check = require_login()
    if auth_check:
        return auth_check
    flash('صفحة طلبات الصيانة قيد التطوير', 'info')
    return redirect(url_for('dashboard'))

@app.route('/parts')
def parts():
    auth_check = require_login()
    if auth_check:
        return auth_check
    flash('صفحة قطع الغيار قيد التطوير', 'info')
    return redirect(url_for('dashboard'))

@app.route('/inventory')
def inventory():
    auth_check = require_login()
    if auth_check:
        return auth_check
    flash('صفحة المخزون قيد التطوير', 'info')
    return redirect(url_for('dashboard'))

@app.route('/invoices')
def invoices():
    auth_check = require_login()
    if auth_check:
        return auth_check
    flash('صفحة الفواتير قيد التطوير', 'info')
    return redirect(url_for('dashboard'))

@app.route('/reports')
def reports():
    auth_check = require_login()
    if auth_check:
        return auth_check
    flash('صفحة التقارير قيد التطوير', 'info')
    return redirect(url_for('dashboard'))

@app.route('/add_vehicle')
def add_vehicle():
    auth_check = require_login()
    if auth_check:
        return auth_check
    flash('صفحة إضافة سيارة جديدة قيد التطوير', 'info')
    return redirect(url_for('dashboard'))

@app.route('/add_service_request')
def add_service_request():
    auth_check = require_login()
    if auth_check:
        return auth_check
    flash('صفحة طلب صيانة جديد قيد التطوير', 'info')
    return redirect(url_for('dashboard'))

@app.route('/technicians')
def technicians():
    auth_check = require_login()
    if auth_check:
        return auth_check
    flash('صفحة إدارة الفنيين قيد التطوير', 'info')
    return redirect(url_for('dashboard'))

@app.route('/add_invoice')
def add_invoice():
    auth_check = require_login()
    if auth_check:
        return auth_check
    flash('صفحة إنشاء فاتورة جديدة قيد التطوير', 'info')
    return redirect(url_for('dashboard'))

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

if __name__ == '__main__':
    print("=" * 60)
    print("🚗 نظام إدارة مركز صيانة السيارات - التطبيق الشامل")
    print("Car Center Management System - Complete Application")
    print("=" * 60)
    print("🚀 بدء تشغيل النظام...")
    print("📍 الرابط: http://127.0.0.1:5000")
    print("👤 المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("🏠 لوحة التحكم + 📋 إدارة العملاء")
    print("✅ جميع الأخطاء تم إصلاحها")
    print("=" * 60)
    print("🌐 سيتم فتح المتصفح تلقائياً...")
    
    # Start browser in a separate thread
    threading.Thread(target=open_browser, daemon=True).start()
    
    # Run the app
    app.run(debug=True, host='127.0.0.1', port=5000, use_reloader=False)
