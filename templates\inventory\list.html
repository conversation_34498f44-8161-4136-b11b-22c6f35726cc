{% extends "base.html" %}

{% block title %}إدارة المخزون - مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-box-seam me-2"></i>
            إدارة المخزون
        </h1>
    </div>
</div>

<!-- Low Stock Alert -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <strong>تنبيه:</strong> يوجد {{ inventory_items|selectattr('quantity', 'le', inventory_items|map(attribute='min_quantity'))|list|length }} قطعة غيار تحتاج إلى إعادة تموين.
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if inventory_items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>قطعة الغيار</th>
                                <th>رقم القطعة</th>
                                <th>الكمية الحالية</th>
                                <th>الحد الأدنى</th>
                                <th>الحالة</th>
                                <th>آخر تحديث</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in inventory_items %}
                            <tr class="{% if item.quantity <= item.min_quantity %}table-warning{% endif %}">
                                <td>
                                    <strong>{{ item.part.name }}</strong>
                                    {% if item.part.description %}
                                    <br><small class="text-muted">{{ item.part.description[:50] }}...</small>
                                    {% endif %}
                                </td>
                                <td>{{ item.part.part_number or '-' }}</td>
                                <td>
                                    <span class="badge {% if item.quantity <= item.min_quantity %}bg-danger{% else %}bg-success{% endif %}">
                                        {{ item.quantity }}
                                    </span>
                                </td>
                                <td>{{ item.min_quantity }}</td>
                                <td>
                                    {% if item.quantity <= item.min_quantity %}
                                        <span class="badge bg-danger">نفاد المخزون</span>
                                    {% elif item.quantity <= item.min_quantity * 2 %}
                                        <span class="badge bg-warning">مخزون منخفض</span>
                                    {% else %}
                                        <span class="badge bg-success">متوفر</span>
                                    {% endif %}
                                </td>
                                <td>{{ item.last_updated.strftime('%Y-%m-%d') if item.last_updated else '-' }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" title="تحديث الكمية">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-outline-success" title="إضافة مخزون">
                                            <i class="bi bi-plus-lg"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-box-seam display-1 text-muted"></i>
                    <h4 class="mt-3">لا توجد عناصر في المخزون</h4>
                    <p class="text-muted">ابدأ بإضافة قطع غيار إلى المخزون</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
