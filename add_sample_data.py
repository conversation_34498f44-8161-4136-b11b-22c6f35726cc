#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إضافة بيانات تجريبية لنظام إدارة مركز صيانة السيارات
"""

from datetime import datetime, timedelta
import random

# Import the app and models
from app import app
from models import db, User, Customer, Vehicle, ServiceRequest, Technician, Service, Part, Inventory, Invoice, Payment

def add_sample_data():
    """إضافة بيانات تجريبية للنظام"""
    
    with app.app_context():
        print("إضافة بيانات تجريبية...")
        
        # إضافة فنيين
        technicians_data = [
            {"name": "أحمد محمد", "phone": "0501234567", "specialization": "محركات", "salary": 5000},
            {"name": "محمد علي", "phone": "0507654321", "specialization": "كهرباء", "salary": 4500},
            {"name": "علي حسن", "phone": "0509876543", "specialization": "تكييف", "salary": 4000},
            {"name": "حسن أحمد", "phone": "0502468135", "specialization": "فرامل", "salary": 4200},
        ]
        
        for tech_data in technicians_data:
            if not Technician.query.filter_by(name=tech_data["name"]).first():
                technician = Technician(**tech_data)
                db.session.add(technician)
        
        # إضافة خدمات
        services_data = [
            {"name": "تغيير زيت المحرك", "category": "maintenance", "price": 150, "estimated_time": 30},
            {"name": "فحص شامل", "category": "maintenance", "price": 200, "estimated_time": 60},
            {"name": "إصلاح المحرك", "category": "repair", "price": 800, "estimated_time": 240},
            {"name": "تغيير فلتر الهواء", "category": "replacement", "price": 80, "estimated_time": 15},
            {"name": "إصلاح الفرامل", "category": "repair", "price": 400, "estimated_time": 120},
            {"name": "تغيير إطارات", "category": "replacement", "price": 600, "estimated_time": 45},
        ]
        
        for service_data in services_data:
            if not Service.query.filter_by(name=service_data["name"]).first():
                service = Service(**service_data)
                db.session.add(service)
        
        # إضافة قطع غيار
        parts_data = [
            {"name": "زيت محرك", "part_number": "OIL001", "category": "زيوت", "unit_price": 45, "supplier": "شركة الزيوت المتحدة"},
            {"name": "فلتر هواء", "part_number": "AIR001", "category": "فلاتر", "unit_price": 35, "supplier": "مؤسسة الفلاتر"},
            {"name": "فحمات فرامل", "part_number": "BRK001", "category": "فرامل", "unit_price": 120, "supplier": "شركة الفرامل الذهبية"},
            {"name": "إطار", "part_number": "TIR001", "category": "إطارات", "unit_price": 250, "supplier": "مؤسسة الإطارات"},
            {"name": "بطارية", "part_number": "BAT001", "category": "كهرباء", "unit_price": 300, "supplier": "شركة البطاريات"},
        ]
        
        for part_data in parts_data:
            if not Part.query.filter_by(part_number=part_data["part_number"]).first():
                part = Part(**part_data)
                db.session.add(part)
        
        db.session.commit()
        
        # إضافة مخزون لقطع الغيار
        parts = Part.query.all()
        for part in parts:
            if not part.inventory:
                inventory = Inventory(
                    part_id=part.id,
                    quantity=random.randint(20, 100),
                    min_quantity=random.randint(5, 15)
                )
                db.session.add(inventory)
        
        # إضافة عملاء
        customers_data = [
            {"name": "خالد العتيبي", "phone": "0551234567", "email": "<EMAIL>", "address": "الرياض، حي النخيل"},
            {"name": "سعد المطيري", "phone": "0557654321", "email": "<EMAIL>", "address": "جدة، حي الصفا"},
            {"name": "فهد القحطاني", "phone": "0559876543", "email": "<EMAIL>", "address": "الدمام، حي الفيصلية"},
            {"name": "عبدالله الشمري", "phone": "0552468135", "email": "<EMAIL>", "address": "مكة، حي العزيزية"},
            {"name": "محمد الغامدي", "phone": "0558642097", "email": "<EMAIL>", "address": "المدينة، حي قباء"},
        ]
        
        for customer_data in customers_data:
            if not Customer.query.filter_by(phone=customer_data["phone"]).first():
                customer = Customer(**customer_data)
                db.session.add(customer)
        
        db.session.commit()
        
        # إضافة سيارات
        customers = Customer.query.all()
        vehicles_data = [
            {"make": "تويوتا", "model": "كامري", "year": 2020, "plate_number": "أ ب ج 123", "color": "أبيض"},
            {"make": "هوندا", "model": "أكورد", "year": 2019, "plate_number": "د هـ و 456", "color": "أسود"},
            {"make": "نيسان", "model": "التيما", "year": 2021, "plate_number": "ز ح ط 789", "color": "فضي"},
            {"make": "هيونداي", "model": "إلنترا", "year": 2018, "plate_number": "ي ك ل 321", "color": "أزرق"},
            {"make": "كيا", "model": "أوبتيما", "year": 2022, "plate_number": "م ن س 654", "color": "أحمر"},
        ]
        
        for i, vehicle_data in enumerate(vehicles_data):
            if i < len(customers):
                vehicle_data["customer_id"] = customers[i].id
                vehicle_data["current_mileage"] = random.randint(10000, 150000)
                if not Vehicle.query.filter_by(plate_number=vehicle_data["plate_number"]).first():
                    vehicle = Vehicle(**vehicle_data)
                    db.session.add(vehicle)
        
        db.session.commit()
        
        # إضافة طلبات صيانة
        vehicles = Vehicle.query.all()
        technicians = Technician.query.all()
        
        for vehicle in vehicles[:3]:  # إضافة طلبات لأول 3 سيارات
            request_data = {
                "vehicle_id": vehicle.id,
                "technician_id": random.choice(technicians).id if technicians else None,
                "service_type": random.choice(["maintenance", "repair", "replacement"]),
                "description": "صيانة دورية للسيارة",
                "status": random.choice(["pending", "in_progress", "completed"]),
                "mileage_at_service": vehicle.current_mileage + random.randint(100, 1000),
                "request_date": datetime.now() - timedelta(days=random.randint(1, 30))
            }
            
            service_request = ServiceRequest(**request_data)
            db.session.add(service_request)
        
        db.session.commit()
        print("✓ تم إضافة البيانات التجريبية بنجاح!")
        
        # طباعة ملخص البيانات
        print("\nملخص البيانات المضافة:")
        print(f"- العملاء: {Customer.query.count()}")
        print(f"- السيارات: {Vehicle.query.count()}")
        print(f"- الفنيين: {Technician.query.count()}")
        print(f"- الخدمات: {Service.query.count()}")
        print(f"- قطع الغيار: {Part.query.count()}")
        print(f"- طلبات الصيانة: {ServiceRequest.query.count()}")

if __name__ == "__main__":
    add_sample_data()
