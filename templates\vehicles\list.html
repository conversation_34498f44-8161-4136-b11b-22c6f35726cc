{% extends "base.html" %}

{% block title %}قائمة السيارات - مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-car-front me-2"></i>
                قائمة السيارات
            </h1>
            <a href="{{ url_for('add_vehicle') }}" class="btn btn-primary">
                <i class="bi bi-car-front-fill me-2"></i>
                إضافة سيارة جديدة
            </a>
        </div>
    </div>
</div>

<!-- Vehicles Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if vehicles.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم اللوحة</th>
                                <th>المالك</th>
                                <th>الماركة والموديل</th>
                                <th>سنة الصنع</th>
                                <th>قراءة العداد</th>
                                <th>اللون</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for vehicle in vehicles.items %}
                            <tr>
                                <td>
                                    <strong>{{ vehicle.plate_number }}</strong>
                                    {% if vehicle.chassis_number %}
                                    <br><small class="text-muted">{{ vehicle.chassis_number }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>{{ vehicle.owner.name }}</strong>
                                    <br><small class="text-muted">{{ vehicle.owner.phone }}</small>
                                </td>
                                <td>{{ vehicle.make }} {{ vehicle.model }}</td>
                                <td>{{ vehicle.year }}</td>
                                <td>{{ "{:,}".format(vehicle.current_mileage) }} كم</td>
                                <td>{{ vehicle.color or '-' }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="#" class="btn btn-outline-info" title="عرض التفاصيل">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="#" class="btn btn-outline-primary" title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="{{ url_for('add_service_request') }}?vehicle_id={{ vehicle.id }}" 
                                           class="btn btn-outline-success" title="طلب صيانة">
                                            <i class="bi bi-tools"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if vehicles.pages > 1 %}
                <nav aria-label="صفحات السيارات">
                    <ul class="pagination justify-content-center">
                        {% if vehicles.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('vehicles', page=vehicles.prev_num) }}">السابق</a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in vehicles.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != vehicles.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('vehicles', page=page_num) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if vehicles.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('vehicles', page=vehicles.next_num) }}">التالي</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-car-front display-1 text-muted"></i>
                    <h4 class="mt-3">لا توجد سيارات</h4>
                    <p class="text-muted">ابدأ بإضافة سيارة جديدة</p>
                    <a href="{{ url_for('add_vehicle') }}" class="btn btn-primary">
                        <i class="bi bi-car-front-fill me-2"></i>
                        إضافة سيارة جديدة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
