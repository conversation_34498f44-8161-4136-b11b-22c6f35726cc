#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إدارة مركز صيانة السيارات - النسخة الأساسية
Car Center Management System - Minimal Version
"""

from flask import Flask, render_template, redirect, url_for, flash, request, session
from datetime import datetime

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'car-center-secret-key-2024'

# Simple authentication
def is_logged_in():
    return session.get('logged_in', False)

def require_login():
    if not is_logged_in():
        return redirect(url_for('login'))
    return None

# Routes
@app.route('/')
def index():
    if is_logged_in():
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if is_logged_in():
        return redirect(url_for('dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        # Simple authentication
        if username == 'admin' and password == 'admin123':
            session['logged_in'] = True
            session['username'] = username
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('login_simple.html')

@app.route('/dashboard')
def dashboard():
    auth_check = require_login()
    if auth_check:
        return auth_check
    
    # بيانات تجريبية لعرض لوحة التحكم
    stats = {
        'total_customers': 45,
        'total_vehicles': 67,
        'pending_requests': 8,
        'monthly_revenue': 25000
    }
    
    # طلبات صيانة تجريبية
    class MockRequest:
        def __init__(self, customer_name, car_make, car_model, date, status):
            self.vehicle = MockVehicle(customer_name, car_make, car_model)
            self.request_date = datetime.strptime(date, '%Y-%m-%d')
            self.status = status
    
    class MockVehicle:
        def __init__(self, owner_name, make, model):
            self.owner = MockOwner(owner_name)
            self.make = make
            self.model = model
    
    class MockOwner:
        def __init__(self, name):
            self.name = name
    
    recent_requests = [
        MockRequest("أحمد محمد", "تويوتا", "كامري", "2024-01-15", "pending"),
        MockRequest("سعد العتيبي", "هوندا", "أكورد", "2024-01-14", "in_progress"),
        MockRequest("محمد الغامدي", "نيسان", "التيما", "2024-01-13", "completed"),
        MockRequest("خالد القحطاني", "هيونداي", "إلنترا", "2024-01-12", "pending"),
        MockRequest("فهد الشمري", "كيا", "أوبتيما", "2024-01-11", "completed"),
    ]
    
    # عناصر مخزون منخفضة تجريبية
    class MockInventoryItem:
        def __init__(self, part_name, part_number, quantity, min_quantity):
            self.part = MockPart(part_name, part_number)
            self.quantity = quantity
            self.min_quantity = min_quantity
    
    class MockPart:
        def __init__(self, name, part_number):
            self.name = name
            self.part_number = part_number
    
    low_stock_items = [
        MockInventoryItem("زيت محرك", "OIL001", 5, 10),
        MockInventoryItem("فلتر هواء", "AIR001", 3, 15),
        MockInventoryItem("فحمات فرامل", "BRK001", 2, 8),
    ]
    
    return render_template('dashboard.html',
                         stats=stats,
                         recent_requests=recent_requests,
                         low_stock_items=low_stock_items)

# Simple routes for navigation
@app.route('/customers')
def customers():
    auth_check = require_login()
    if auth_check:
        return auth_check
    return "<h1>إدارة العملاء</h1><p>قيد التطوير</p><a href='/dashboard'>العودة للوحة التحكم</a>"

@app.route('/vehicles')
def vehicles():
    auth_check = require_login()
    if auth_check:
        return auth_check
    return "<h1>إدارة السيارات</h1><p>قيد التطوير</p><a href='/dashboard'>العودة للوحة التحكم</a>"

@app.route('/service_requests')
def service_requests():
    auth_check = require_login()
    if auth_check:
        return auth_check
    return "<h1>إدارة طلبات الصيانة</h1><p>قيد التطوير</p><a href='/dashboard'>العودة للوحة التحكم</a>"

@app.route('/parts')
def parts():
    auth_check = require_login()
    if auth_check:
        return auth_check
    return "<h1>إدارة قطع الغيار</h1><p>قيد التطوير</p><a href='/dashboard'>العودة للوحة التحكم</a>"

@app.route('/inventory')
def inventory():
    auth_check = require_login()
    if auth_check:
        return auth_check
    return "<h1>إدارة المخزون</h1><p>قيد التطوير</p><a href='/dashboard'>العودة للوحة التحكم</a>"

@app.route('/invoices')
def invoices():
    auth_check = require_login()
    if auth_check:
        return auth_check
    return "<h1>إدارة الفواتير</h1><p>قيد التطوير</p><a href='/dashboard'>العودة للوحة التحكم</a>"

@app.route('/reports')
def reports():
    auth_check = require_login()
    if auth_check:
        return auth_check
    return "<h1>التقارير</h1><p>قيد التطوير</p><a href='/dashboard'>العودة للوحة التحكم</a>"

@app.route('/add_customer')
def add_customer():
    auth_check = require_login()
    if auth_check:
        return auth_check
    return "<h1>إضافة عميل جديد</h1><p>قيد التطوير</p><a href='/dashboard'>العودة للوحة التحكم</a>"

@app.route('/add_vehicle')
def add_vehicle():
    auth_check = require_login()
    if auth_check:
        return auth_check
    return "<h1>إضافة سيارة جديدة</h1><p>قيد التطوير</p><a href='/dashboard'>العودة للوحة التحكم</a>"

@app.route('/add_service_request')
def add_service_request():
    auth_check = require_login()
    if auth_check:
        return auth_check
    return "<h1>طلب صيانة جديد</h1><p>قيد التطوير</p><a href='/dashboard'>العودة للوحة التحكم</a>"

@app.route('/technicians')
def technicians():
    auth_check = require_login()
    if auth_check:
        return auth_check
    return "<h1>إدارة الفنيين</h1><p>قيد التطوير</p><a href='/dashboard'>العودة للوحة التحكم</a>"

@app.route('/add_invoice')
def add_invoice():
    auth_check = require_login()
    if auth_check:
        return auth_check
    return "<h1>إنشاء فاتورة جديدة</h1><p>قيد التطوير</p><a href='/dashboard'>العودة للوحة التحكم</a>"

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

if __name__ == '__main__':
    print("🚀 بدء تشغيل نظام إدارة مركز صيانة السيارات...")
    print("📍 الرابط: http://127.0.0.1:5000")
    print("👤 المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("=" * 50)
    
    app.run(debug=True, host='127.0.0.1', port=5000)
