{% extends "base.html" %}

{% block title %}طلبات الصيانة - مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-tools me-2"></i>
                طلبات الصيانة
            </h1>
            <a href="{{ url_for('add_service_request') }}" class="btn btn-primary">
                <i class="bi bi-plus-lg me-2"></i>
                طلب صيانة جديد
            </a>
        </div>
    </div>
</div>

<!-- Status Filter -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="btn-group" role="group">
            <input type="radio" class="btn-check" name="status-filter" id="all" autocomplete="off" checked>
            <label class="btn btn-outline-primary" for="all">الكل</label>
            
            <input type="radio" class="btn-check" name="status-filter" id="pending" autocomplete="off">
            <label class="btn btn-outline-warning" for="pending">قيد الانتظار</label>
            
            <input type="radio" class="btn-check" name="status-filter" id="in_progress" autocomplete="off">
            <label class="btn btn-outline-info" for="in_progress">قيد التنفيذ</label>
            
            <input type="radio" class="btn-check" name="status-filter" id="completed" autocomplete="off">
            <label class="btn btn-outline-success" for="completed">مكتملة</label>
        </div>
    </div>
</div>

<!-- Service Requests Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if requests.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>العميل والسيارة</th>
                                <th>نوع الخدمة</th>
                                <th>الفني</th>
                                <th>تاريخ الطلب</th>
                                <th>الموعد المحدد</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for request in requests.items %}
                            <tr data-status="{{ request.status }}">
                                <td><strong>#{{ request.id }}</strong></td>
                                <td>
                                    <strong>{{ request.vehicle.owner.name }}</strong>
                                    <br>
                                    <small class="text-muted">
                                        {{ request.vehicle.make }} {{ request.vehicle.model }} 
                                        ({{ request.vehicle.plate_number }})
                                    </small>
                                </td>
                                <td>
                                    {% if request.service_type == 'maintenance' %}
                                        <span class="badge bg-info">صيانة دورية</span>
                                    {% elif request.service_type == 'repair' %}
                                        <span class="badge bg-warning">إصلاح</span>
                                    {% elif request.service_type == 'replacement' %}
                                        <span class="badge bg-secondary">استبدال قطع غيار</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if request.technician %}
                                        {{ request.technician.name }}
                                    {% else %}
                                        <span class="text-muted">لم يتم التحديد</span>
                                    {% endif %}
                                </td>
                                <td>{{ request.request_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if request.scheduled_date %}
                                        {{ request.scheduled_date.strftime('%Y-%m-%d %H:%M') }}
                                    {% else %}
                                        <span class="text-muted">لم يتم التحديد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if request.status == 'pending' %}
                                        <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif request.status == 'in_progress' %}
                                        <span class="badge bg-info">قيد التنفيذ</span>
                                    {% elif request.status == 'completed' %}
                                        <span class="badge bg-success">مكتملة</span>
                                    {% elif request.status == 'cancelled' %}
                                        <span class="badge bg-danger">ملغية</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="#" class="btn btn-outline-info" title="عرض التفاصيل">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        {% if request.status != 'completed' %}
                                        <a href="#" class="btn btn-outline-primary" title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        {% endif %}
                                        {% if request.status == 'completed' and not request.invoice %}
                                        <a href="#" class="btn btn-outline-success" title="إنشاء فاتورة">
                                            <i class="bi bi-receipt"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if requests.pages > 1 %}
                <nav aria-label="صفحات طلبات الصيانة">
                    <ul class="pagination justify-content-center">
                        {% if requests.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('service_requests', page=requests.prev_num) }}">السابق</a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in requests.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != requests.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('service_requests', page=page_num) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if requests.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('service_requests', page=requests.next_num) }}">التالي</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-tools display-1 text-muted"></i>
                    <h4 class="mt-3">لا توجد طلبات صيانة</h4>
                    <p class="text-muted">ابدأ بإضافة طلب صيانة جديد</p>
                    <a href="{{ url_for('add_service_request') }}" class="btn btn-primary">
                        <i class="bi bi-plus-lg me-2"></i>
                        طلب صيانة جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Filter by status
document.querySelectorAll('input[name="status-filter"]').forEach(function(radio) {
    radio.addEventListener('change', function() {
        var status = this.id;
        var rows = document.querySelectorAll('tbody tr[data-status]');
        
        rows.forEach(function(row) {
            if (status === 'all' || row.dataset.status === status) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });
});
</script>
{% endblock %}
