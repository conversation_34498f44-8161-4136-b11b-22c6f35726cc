{% extends "base.html" %}

{% block title %}لوحة التحكم - مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-speedometer2 me-2"></i>
            لوحة التحكم
        </h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_customers }}</h4>
                        <p class="card-text">إجمالي العملاء</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-people fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_vehicles }}</h4>
                        <p class="card-text">إجمالي السيارات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-car-front fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.pending_requests }}</h4>
                        <p class="card-text">طلبات قيد الانتظار</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clock fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.monthly_revenue }}</h4>
                        <p class="card-text">إيرادات الشهر</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-currency-dollar fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning-fill me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('add_customer') }}" class="btn btn-outline-primary w-100">
                            <i class="bi bi-person-plus me-2"></i>
                            إضافة عميل جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('add_vehicle') }}" class="btn btn-outline-success w-100">
                            <i class="bi bi-car-front-fill me-2"></i>
                            إضافة سيارة جديدة
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('add_service_request') }}" class="btn btn-outline-warning w-100">
                            <i class="bi bi-tools me-2"></i>
                            طلب صيانة جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('invoices') }}" class="btn btn-outline-info w-100">
                            <i class="bi bi-receipt me-2"></i>
                            إنشاء فاتورة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-clock-history me-2"></i>
                    طلبات الصيانة الأخيرة
                </h5>
            </div>
            <div class="card-body">
                {% if recent_requests %}
                    <div class="list-group list-group-flush">
                        {% for request in recent_requests %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ request.vehicle.owner.name }}</h6>
                                <p class="mb-1 text-muted">{{ request.vehicle.make }} {{ request.vehicle.model }}</p>
                                <small class="text-muted">{{ request.request_date.strftime('%Y-%m-%d') }}</small>
                            </div>
                            <span class="badge bg-{{ 'warning' if request.status == 'pending' else 'success' if request.status == 'completed' else 'primary' }}">
                                {{ request.status }}
                            </span>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted text-center">لا توجد طلبات صيانة حديثة</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    تنبيهات المخزون
                </h5>
            </div>
            <div class="card-body">
                {% if low_stock_items %}
                    <div class="list-group list-group-flush">
                        {% for item in low_stock_items %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ item.part.name }}</h6>
                                <small class="text-muted">{{ item.part.part_number }}</small>
                            </div>
                            <span class="badge bg-danger">{{ item.quantity }} متبقي</span>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted text-center">جميع المواد متوفرة في المخزون</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
