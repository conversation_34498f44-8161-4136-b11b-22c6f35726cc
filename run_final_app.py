#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشغيل التطبيق النهائي لمركز صيانة السيارات
Run Final Car Center Management System
"""

import webbrowser
import threading
import time
import os
import sys

def open_browser():
    """فتح المتصفح تلقائياً بعد تشغيل الخادم"""
    time.sleep(2)
    webbrowser.open('http://1********:5000')

def main():
    """تشغيل التطبيق الرئيسي"""
    print("=" * 60)
    print("🚗 مركز صيانة السيارات - النظام النهائي")
    print("Car Center Management System - Final Version")
    print("=" * 60)
    print()
    
    # التحقق من وجود الملف
    if not os.path.exists('final_app.py'):
        print("❌ خطأ: لم يتم العثور على ملف final_app.py")
        print("❌ Error: final_app.py file not found")
        sys.exit(1)
    
    try:
        # استيراد التطبيق
        from final_app import app
        
        print("✅ تم تحميل التطبيق بنجاح")
        print("✅ Application loaded successfully")
        print()
        print("📋 معلومات تسجيل الدخول:")
        print("📋 Login Information:")
        print("   👤 اسم المستخدم / Username: admin")
        print("   🔑 كلمة المرور / Password: admin123")
        print()
        print("🌐 سيتم فتح المتصفح تلقائياً على:")
        print("🌐 Browser will open automatically at:")
        print("   http://1********:5000")
        print()
        print("⏹️  للإيقاف اضغط Ctrl+C")
        print("⏹️  To stop press Ctrl+C")
        print("=" * 60)
        
        # فتح المتصفح في خيط منفصل
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # تشغيل الخادم
        app.run(
            debug=False,  # إيقاف وضع التطوير لتجنب الأخطاء
            host='1********',
            port=5000,
            use_reloader=False  # إيقاف إعادة التحميل التلقائي
        )
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
        print(f"❌ Import error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        print(f"❌ Application error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
