#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار لوحة التحكم المحدثة
"""

from flask import Flask, render_template
from datetime import datetime

app = Flask(__name__)
app.config['SECRET_KEY'] = 'test-key'

@app.route('/')
def dashboard():
    # بيانات تجريبية لاختبار لوحة التحكم
    stats = {
        'total_customers': 45,
        'total_vehicles': 67,
        'pending_requests': 8,
        'monthly_revenue': 25000
    }
    
    # طلبات صيانة تجريبية
    class MockRequest:
        def __init__(self, customer_name, car_make, car_model, date, status):
            self.vehicle = MockVehicle(customer_name, car_make, car_model)
            self.request_date = datetime.strptime(date, '%Y-%m-%d')
            self.status = status
    
    class MockVehicle:
        def __init__(self, owner_name, make, model):
            self.owner = MockOwner(owner_name)
            self.make = make
            self.model = model
    
    class MockOwner:
        def __init__(self, name):
            self.name = name
    
    recent_requests = [
        MockRequest("أحمد محمد", "تويوتا", "كامري", "2024-01-15", "pending"),
        MockRequest("سعد العتيبي", "هوندا", "أكورد", "2024-01-14", "in_progress"),
        MockRequest("محمد الغامدي", "نيسان", "التيما", "2024-01-13", "completed"),
        MockRequest("خالد القحطاني", "هيونداي", "إلنترا", "2024-01-12", "pending"),
        MockRequest("فهد الشمري", "كيا", "أوبتيما", "2024-01-11", "completed"),
    ]
    
    # عناصر مخزون منخفضة تجريبية
    class MockInventoryItem:
        def __init__(self, part_name, part_number, quantity, min_quantity):
            self.part = MockPart(part_name, part_number)
            self.quantity = quantity
            self.min_quantity = min_quantity
    
    class MockPart:
        def __init__(self, name, part_number):
            self.name = name
            self.part_number = part_number
    
    low_stock_items = [
        MockInventoryItem("زيت محرك", "OIL001", 5, 10),
        MockInventoryItem("فلتر هواء", "AIR001", 3, 15),
        MockInventoryItem("فحمات فرامل", "BRK001", 2, 8),
    ]
    
    return render_template('dashboard.html', 
                         stats=stats, 
                         recent_requests=recent_requests,
                         low_stock_items=low_stock_items)

if __name__ == '__main__':
    print("🚀 تشغيل اختبار لوحة التحكم المحدثة...")
    print("📍 الرابط: http://127.0.0.1:5001")
    print("🎯 لوحة التحكم الجديدة مع التحسينات")
    app.run(debug=True, host='127.0.0.1', port=5001)
