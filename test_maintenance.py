#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, session, redirect, url_for

app = Flask(__name__)
app.secret_key = 'test_secret_key'

@app.route('/')
def index():
    return redirect(url_for('test_maintenance'))

@app.route('/test_maintenance')
def test_maintenance():
    # محاكاة تسجيل الدخول
    session['logged_in'] = True
    
    # بيانات صيانة تجريبية
    maintenance_requests = [
        {
            'id': 1,
            'customer_name': 'أحمد محمد العلي',
            'vehicle': 'تويوتا كامري 2020',
            'service_type': 'صيانة دورية',
            'status': 'قيد التنفيذ',
            'technician': 'محمد الفني',
            'start_date': '2024-01-15',
            'cost': 450.00,
            'priority': 'متوسط'
        },
        {
            'id': 2,
            'customer_name': 'سعد العتيبي',
            'vehicle': 'هوندا أكورد 2019',
            'service_type': 'إصلاح فرامل',
            'status': 'مكتمل',
            'technician': 'أحمد الفني',
            'start_date': '2024-01-14',
            'cost': 320.00,
            'priority': 'عالي'
        }
    ]
    
    return f'''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>اختبار صفحة الصيانة</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    </head>
    <body>
        <div class="container mt-4">
            <h1 class="text-center text-success mb-4">
                <i class="bi bi-tools me-2"></i>
                اختبار صفحة الصيانة
            </h1>
            
            <div class="alert alert-success">
                <h4>✅ صفحة الصيانة تعمل بشكل صحيح!</h4>
                <p>تم تحميل البيانات التجريبية بنجاح.</p>
            </div>
            
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-clock-history display-6 mb-2"></i>
                            <h4>{len([r for r in maintenance_requests if r['status'] == 'في الانتظار'])}</h4>
                            <p class="mb-0">في الانتظار</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-gear display-6 mb-2"></i>
                            <h4>{len([r for r in maintenance_requests if r['status'] == 'قيد التنفيذ'])}</h4>
                            <p class="mb-0">قيد التنفيذ</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-check-circle display-6 mb-2"></i>
                            <h4>{len([r for r in maintenance_requests if r['status'] == 'مكتمل'])}</h4>
                            <p class="mb-0">مكتمل</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">طلبات الصيانة</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>العميل</th>
                                    <th>السيارة</th>
                                    <th>نوع الخدمة</th>
                                    <th>الحالة</th>
                                    <th>الفني</th>
                                    <th>التكلفة</th>
                                </tr>
                            </thead>
                            <tbody>
    '''
    
    # إضافة صفوف الجدول
    for request in maintenance_requests:
        status_color = {
            'في الانتظار': 'warning',
            'قيد التنفيذ': 'info',
            'مكتمل': 'success'
        }.get(request['status'], 'secondary')
        
        html_content = f'''
                                <tr>
                                    <td><strong>#{request['id']:03d}</strong></td>
                                    <td>{request['customer_name']}</td>
                                    <td>{request['vehicle']}</td>
                                    <td>{request['service_type']}</td>
                                    <td><span class="badge bg-{status_color}">{request['status']}</span></td>
                                    <td>{request['technician']}</td>
                                    <td><strong>{request['cost']:.2f} ريال</strong></td>
                                </tr>
        '''
    
    return html_content + '''
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <a href="/test_maintenance" class="btn btn-primary">
                    <i class="bi bi-arrow-clockwise me-2"></i>
                    إعادة تحميل الصفحة
                </a>
            </div>
        </div>
        
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    '''

if __name__ == '__main__':
    print("🔧 اختبار صفحة الصيانة...")
    print("📍 الرابط: http://127.0.0.1:5001")
    print("✅ إذا ظهرت هذه الصفحة، فإن المشكلة في التطبيق الأصلي")
    app.run(debug=True, port=5001)
