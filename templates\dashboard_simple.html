{% extends "base_simple.html" %}

{% block title %}لوحة التحكم - مركز صيانة السيارات{% endblock %}

{% block content %}
<!-- Welcome Section -->
<div class="welcome-section">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="mb-2">
                <i class="bi bi-speedometer2 me-3"></i>
                مرحباً بك في لوحة التحكم
            </h1>
            <p class="mb-0 fs-5">نظام إدارة مركز صيانة السيارات</p>
            <small class="opacity-75">آخر تحديث: الآن</small>
        </div>
        <div class="col-md-4 text-end">
            <i class="bi bi-car-front-fill" style="font-size: 4rem; opacity: 0.3;"></i>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="dashboard-card">
            <div class="stat-card primary position-relative">
                <i class="bi bi-people stat-icon"></i>
                <div class="position-relative">
                    <div class="stat-number">{{ stats.total_customers }}</div>
                    <div class="stat-label">إجمالي العملاء</div>
                    <small class="opacity-75">
                        <i class="bi bi-arrow-up me-1"></i>
                        +12% من الشهر الماضي
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="dashboard-card">
            <div class="stat-card success position-relative">
                <i class="bi bi-car-front stat-icon"></i>
                <div class="position-relative">
                    <div class="stat-number">{{ stats.total_vehicles }}</div>
                    <div class="stat-label">إجمالي السيارات</div>
                    <small class="opacity-75">
                        <i class="bi bi-arrow-up me-1"></i>
                        +8% من الشهر الماضي
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="dashboard-card">
            <div class="stat-card warning position-relative">
                <i class="bi bi-clock stat-icon"></i>
                <div class="position-relative">
                    <div class="stat-number">{{ stats.pending_requests }}</div>
                    <div class="stat-label">طلبات قيد الانتظار</div>
                    <small class="opacity-75">
                        <i class="bi bi-exclamation-triangle me-1"></i>
                        تحتاج متابعة
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="dashboard-card">
            <div class="stat-card info position-relative">
                <i class="bi bi-currency-dollar stat-icon"></i>
                <div class="position-relative">
                    <div class="stat-number">{{ "{:,.0f}".format(stats.monthly_revenue) if stats.monthly_revenue else '0' }}</div>
                    <div class="stat-label">إيرادات الشهر (ريال)</div>
                    <small class="opacity-75">
                        <i class="bi bi-arrow-up me-1"></i>
                        +15% من الشهر الماضي
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="activity-card">
            <div class="card-header bg-white border-0 pb-0">
                <h5 class="mb-0 text-dark">
                    <i class="bi bi-lightning-fill me-2 text-warning"></i>
                    إجراءات سريعة
                </h5>
                <small class="text-muted">الإجراءات الأكثر استخداماً</small>
            </div>
            <div class="card-body pt-3">
                <div class="row g-3">
                    <div class="col-lg-3 col-md-6">
                        <a href="{{ url_for('add_customer') }}" class="btn btn-outline-primary w-100 quick-action-btn">
                            <i class="bi bi-person-plus me-2"></i>
                            <div>
                                <strong>إضافة عميل جديد</strong>
                                <br><small>تسجيل عميل في النظام</small>
                            </div>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <a href="{{ url_for('add_vehicle') }}" class="btn btn-outline-success w-100 quick-action-btn">
                            <i class="bi bi-car-front-fill me-2"></i>
                            <div>
                                <strong>إضافة سيارة جديدة</strong>
                                <br><small>تسجيل سيارة جديدة</small>
                            </div>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <a href="{{ url_for('add_service_request') }}" class="btn btn-outline-warning w-100 quick-action-btn">
                            <i class="bi bi-tools me-2"></i>
                            <div>
                                <strong>طلب صيانة جديد</strong>
                                <br><small>إنشاء طلب صيانة</small>
                            </div>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <a href="{{ url_for('invoices') }}" class="btn btn-outline-info w-100 quick-action-btn">
                            <i class="bi bi-receipt me-2"></i>
                            <div>
                                <strong>إدارة الفواتير</strong>
                                <br><small>عرض وإنشاء الفواتير</small>
                            </div>
                        </a>
                    </div>
                </div>
                
                <!-- Additional Quick Actions -->
                <hr class="my-4">
                <div class="row g-2">
                    <div class="col-md-2">
                        <a href="{{ url_for('customers') }}" class="btn btn-light w-100 text-center py-2">
                            <i class="bi bi-people d-block fs-4 mb-1"></i>
                            <small>العملاء</small>
                        </a>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ url_for('vehicles') }}" class="btn btn-light w-100 text-center py-2">
                            <i class="bi bi-car-front d-block fs-4 mb-1"></i>
                            <small>السيارات</small>
                        </a>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ url_for('service_requests') }}" class="btn btn-light w-100 text-center py-2">
                            <i class="bi bi-tools d-block fs-4 mb-1"></i>
                            <small>الصيانة</small>
                        </a>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ url_for('parts') }}" class="btn btn-light w-100 text-center py-2">
                            <i class="bi bi-box d-block fs-4 mb-1"></i>
                            <small>قطع الغيار</small>
                        </a>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ url_for('inventory') }}" class="btn btn-light w-100 text-center py-2">
                            <i class="bi bi-box-seam d-block fs-4 mb-1"></i>
                            <small>المخزون</small>
                        </a>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ url_for('reports') }}" class="btn btn-light w-100 text-center py-2">
                            <i class="bi bi-graph-up d-block fs-4 mb-1"></i>
                            <small>التقارير</small>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="activity-card">
            <div class="card-header bg-white border-0">
                <h5 class="mb-0 text-dark">
                    <i class="bi bi-clock-history me-2 text-primary"></i>
                    طلبات الصيانة الأخيرة
                </h5>
                <small class="text-muted">آخر 5 طلبات</small>
            </div>
            <div class="card-body pt-0">
                {% if recent_requests %}
                    <div class="list-group list-group-flush">
                        {% for request in recent_requests %}
                        <div class="list-group-item d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-1">
                                    <i class="bi bi-person-circle me-2 text-primary"></i>
                                    <h6 class="mb-0">{{ request.vehicle.owner.name }}</h6>
                                </div>
                                <p class="mb-1 text-muted">
                                    <i class="bi bi-car-front me-1"></i>
                                    {{ request.vehicle.make }} {{ request.vehicle.model }}
                                </p>
                                <small class="text-muted">
                                    <i class="bi bi-calendar me-1"></i>
                                    {{ request.request_date.strftime('%Y-%m-%d') }}
                                </small>
                            </div>
                            <div class="text-end">
                                <span class="badge badge-status bg-{{ 'warning' if request.status == 'pending' else 'success' if request.status == 'completed' else 'primary' }}">
                                    {% if request.status == 'pending' %}
                                        قيد الانتظار
                                    {% elif request.status == 'completed' %}
                                        مكتملة
                                    {% elif request.status == 'in_progress' %}
                                        قيد التنفيذ
                                    {% else %}
                                        {{ request.status }}
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('service_requests') }}" class="btn btn-outline-primary btn-sm">
                            عرض جميع الطلبات
                            <i class="bi bi-arrow-left me-1"></i>
                        </a>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-inbox display-4 text-muted"></i>
                        <h6 class="mt-3 text-muted">لا توجد طلبات صيانة حديثة</h6>
                        <a href="{{ url_for('add_service_request') }}" class="btn btn-primary btn-sm mt-2">
                            <i class="bi bi-plus me-1"></i>
                            إضافة طلب جديد
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="activity-card">
            <div class="card-header bg-white border-0">
                <h5 class="mb-0 text-dark">
                    <i class="bi bi-exclamation-triangle me-2 text-warning"></i>
                    تنبيهات المخزون
                </h5>
                <small class="text-muted">المواد منخفضة المخزون</small>
            </div>
            <div class="card-body pt-0">
                {% if low_stock_items %}
                    <div class="list-group list-group-flush">
                        {% for item in low_stock_items %}
                        <div class="list-group-item d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-1">
                                    <i class="bi bi-box me-2 text-warning"></i>
                                    <h6 class="mb-0">{{ item.part.name }}</h6>
                                </div>
                                <p class="mb-1 text-muted">
                                    <i class="bi bi-hash me-1"></i>
                                    {{ item.part.part_number }}
                                </p>
                                <small class="text-muted">
                                    <i class="bi bi-info-circle me-1"></i>
                                    الحد الأدنى: {{ item.min_quantity }}
                                </small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-danger badge-status">
                                    {{ item.quantity }} متبقي
                                </span>
                                <br>
                                <small class="text-danger">
                                    <i class="bi bi-exclamation-triangle me-1"></i>
                                    يحتاج تموين
                                </small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('inventory') }}" class="btn btn-outline-warning btn-sm">
                            إدارة المخزون
                            <i class="bi bi-arrow-left me-1"></i>
                        </a>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-check-circle display-4 text-success"></i>
                        <h6 class="mt-3 text-success">جميع المواد متوفرة في المخزون</h6>
                        <p class="text-muted">لا توجد مواد تحتاج إعادة تموين</p>
                        <a href="{{ url_for('inventory') }}" class="btn btn-outline-success btn-sm mt-2">
                            <i class="bi bi-box-seam me-1"></i>
                            عرض المخزون
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Additional Dashboard Widgets -->
<div class="row">
    <div class="col-12">
        <div class="activity-card">
            <div class="card-header bg-white border-0">
                <h5 class="mb-0 text-dark">
                    <i class="bi bi-graph-up me-2 text-success"></i>
                    نظرة عامة على الأداء
                </h5>
                <small class="text-muted">إحصائيات سريعة</small>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <div class="border-end">
                            <h4 class="text-primary mb-1">{{ stats.total_customers + stats.total_vehicles }}</h4>
                            <small class="text-muted">إجمالي السجلات</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="border-end">
                            <h4 class="text-success mb-1">95%</h4>
                            <small class="text-muted">معدل رضا العملاء</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="border-end">
                            <h4 class="text-info mb-1">24</h4>
                            <small class="text-muted">ساعة متوسط الخدمة</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <h4 class="text-warning mb-1">{{ stats.pending_requests }}</h4>
                        <small class="text-muted">طلبات في الانتظار</small>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <a href="{{ url_for('reports') }}" class="btn btn-primary">
                        <i class="bi bi-graph-up me-2"></i>
                        عرض التقارير التفصيلية
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
