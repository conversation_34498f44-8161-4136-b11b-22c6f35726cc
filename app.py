from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_login import <PERSON>ginMana<PERSON>, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash
from datetime import datetime, timedelta
from sqlalchemy import func, or_
import os

from config import Config
from models import db, User, Customer, Vehicle, ServiceRequest, Technician, Service, Part, Inventory, Invoice, Payment
from forms import (LoginForm, CustomerForm, VehicleForm, TechnicianForm, ServiceForm, 
                   PartForm, InventoryForm, ServiceRequestForm, InvoiceForm, PaymentForm, SearchForm)

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # Initialize extensions
    db.init_app(app)
    
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # Create tables and default user
    with app.app_context():
        db.create_all()
        
        # Create default admin user if not exists
        if not User.query.filter_by(username='admin').first():
            admin = User(
                username='admin',
                email='<EMAIL>',
                role='admin'
            )
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print("تم إنشاء المستخدم الافتراضي: admin / admin123")
    
    return app

app = create_app()

# Routes
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user and user.check_password(form.password.data):
            login_user(user)
            flash('تم تسجيل الدخول بنجاح!', 'success')
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('dashboard'))
        flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('login.html', form=form)

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    # Get statistics
    stats = {
        'total_customers': Customer.query.count(),
        'total_vehicles': Vehicle.query.count(),
        'pending_requests': ServiceRequest.query.filter_by(status='pending').count(),
        'monthly_revenue': db.session.query(func.sum(Invoice.total_amount)).filter(
            Invoice.invoice_date >= datetime.now().replace(day=1)
        ).scalar() or 0
    }
    
    # Get recent service requests
    recent_requests = ServiceRequest.query.order_by(ServiceRequest.request_date.desc()).limit(5).all()
    
    # Get low stock items
    low_stock_items = Inventory.query.filter(Inventory.quantity <= Inventory.min_quantity).all()
    
    return render_template('dashboard.html', 
                         stats=stats, 
                         recent_requests=recent_requests,
                         low_stock_items=low_stock_items)

# Customer routes
@app.route('/customers')
@login_required
def customers():
    search_form = SearchForm()
    page = request.args.get('page', 1, type=int)
    search_term = request.args.get('search_term', '')
    
    query = Customer.query
    if search_term:
        query = query.filter(or_(
            Customer.name.contains(search_term),
            Customer.phone.contains(search_term),
            Customer.email.contains(search_term)
        ))
    
    customers = query.paginate(
        page=page, per_page=app.config['ITEMS_PER_PAGE'], error_out=False
    )
    
    return render_template('customers/list.html', customers=customers, search_form=search_form)

@app.route('/customers/add', methods=['GET', 'POST'])
@login_required
def add_customer():
    form = CustomerForm()
    if form.validate_on_submit():
        customer = Customer(
            name=form.name.data,
            phone=form.phone.data,
            email=form.email.data,
            address=form.address.data
        )
        db.session.add(customer)
        db.session.commit()
        flash('تم إضافة العميل بنجاح!', 'success')
        return redirect(url_for('customers'))
    
    return render_template('customers/form.html', form=form, title='إضافة عميل جديد')

@app.route('/customers/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_customer(id):
    customer = Customer.query.get_or_404(id)
    form = CustomerForm(obj=customer)
    
    if form.validate_on_submit():
        form.populate_obj(customer)
        db.session.commit()
        flash('تم تحديث بيانات العميل بنجاح!', 'success')
        return redirect(url_for('customers'))
    
    return render_template('customers/form.html', form=form, title='تعديل بيانات العميل')

@app.route('/customers/<int:id>/delete', methods=['POST'])
@login_required
def delete_customer(id):
    customer = Customer.query.get_or_404(id)
    db.session.delete(customer)
    db.session.commit()
    flash('تم حذف العميل بنجاح!', 'success')
    return redirect(url_for('customers'))

# Vehicle routes
@app.route('/vehicles')
@login_required
def vehicles():
    page = request.args.get('page', 1, type=int)
    vehicles = Vehicle.query.paginate(
        page=page, per_page=app.config['ITEMS_PER_PAGE'], error_out=False
    )
    return render_template('vehicles/list.html', vehicles=vehicles)

@app.route('/vehicles/add', methods=['GET', 'POST'])
@login_required
def add_vehicle():
    form = VehicleForm()
    form.customer_id.choices = [(c.id, c.name) for c in Customer.query.all()]
    
    if form.validate_on_submit():
        vehicle = Vehicle(
            customer_id=form.customer_id.data,
            make=form.make.data,
            model=form.model.data,
            year=form.year.data,
            plate_number=form.plate_number.data,
            chassis_number=form.chassis_number.data,
            color=form.color.data,
            current_mileage=form.current_mileage.data or 0
        )
        db.session.add(vehicle)
        db.session.commit()
        flash('تم إضافة السيارة بنجاح!', 'success')
        return redirect(url_for('vehicles'))
    
    return render_template('vehicles/form.html', form=form, title='إضافة سيارة جديدة')

# Service Request routes
@app.route('/service-requests')
@login_required
def service_requests():
    page = request.args.get('page', 1, type=int)
    requests = ServiceRequest.query.order_by(ServiceRequest.request_date.desc()).paginate(
        page=page, per_page=app.config['ITEMS_PER_PAGE'], error_out=False
    )
    return render_template('service_requests/list.html', requests=requests)

@app.route('/service-requests/add', methods=['GET', 'POST'])
@login_required
def add_service_request():
    form = ServiceRequestForm()
    form.vehicle_id.choices = [(v.id, f"{v.owner.name} - {v.make} {v.model} ({v.plate_number})") 
                               for v in Vehicle.query.all()]
    form.technician_id.choices = [(0, 'لم يتم التحديد')] + [(t.id, t.name) 
                                  for t in Technician.query.filter_by(is_active=True).all()]
    
    if form.validate_on_submit():
        service_request = ServiceRequest(
            vehicle_id=form.vehicle_id.data,
            technician_id=form.technician_id.data if form.technician_id.data != 0 else None,
            scheduled_date=form.scheduled_date.data,
            service_type=form.service_type.data,
            description=form.description.data,
            mileage_at_service=form.mileage_at_service.data,
            notes=form.notes.data
        )
        db.session.add(service_request)
        db.session.commit()
        flash('تم إضافة طلب الصيانة بنجاح!', 'success')
        return redirect(url_for('service_requests'))
    
    return render_template('service_requests/form.html', form=form, title='طلب صيانة جديد')

# Additional routes will be added...
@app.route('/services')
@login_required
def services():
    services = Service.query.filter_by(is_active=True).all()
    return render_template('services/list.html', services=services)

@app.route('/parts')
@login_required
def parts():
    parts = Part.query.all()
    return render_template('parts/list.html', parts=parts)

@app.route('/inventory')
@login_required
def inventory():
    inventory_items = Inventory.query.all()
    return render_template('inventory/list.html', inventory_items=inventory_items)

@app.route('/invoices')
@login_required
def invoices():
    invoices = Invoice.query.order_by(Invoice.invoice_date.desc()).all()
    return render_template('invoices/list.html', invoices=invoices)

@app.route('/payments')
@login_required
def payments():
    payments = Payment.query.order_by(Payment.payment_date.desc()).all()
    return render_template('payments/list.html', payments=payments)

@app.route('/reports')
@login_required
def reports():
    return render_template('reports/index.html')

if __name__ == '__main__':
    print("تم إنشاء المستخدم الافتراضي: admin / admin123")
    print("بدء تشغيل الخادم على http://localhost:5000")
    app.run(debug=True, host='127.0.0.1', port=5000)
