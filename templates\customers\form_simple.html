{% extends "customers_base.html" %}

{% block title %}{{ title }} - مركز صيانة السيارات{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="mb-1">
                    <i class="bi bi-person-plus me-2 text-primary"></i>
                    {{ title }}
                </h1>
                <p class="text-muted mb-0">إضافة عميل جديد إلى النظام</p>
            </div>
            <div>
                <a href="{{ url_for('customers') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-person-badge me-2"></i>
                    بيانات العميل الأساسية
                </h5>
            </div>
            <div class="card-body p-4">
                <form method="POST" id="customerForm">
                    
                    <!-- Personal Information Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">
                                <i class="bi bi-person-circle me-2"></i>
                                المعلومات الشخصية
                            </h6>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">
                                <i class="bi bi-person me-1"></i>
                                اسم العميل <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-person"></i>
                                </span>
                                <input type="text" class="form-control" name="name" id="name" required placeholder="أدخل الاسم الكامل">
                            </div>
                            <div class="form-text">أدخل الاسم الكامل للعميل</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">
                                <i class="bi bi-telephone me-1"></i>
                                رقم الهاتف <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-telephone"></i>
                                </span>
                                <input type="tel" class="form-control" name="phone" id="phone" required placeholder="05xxxxxxxx" pattern="[0-9]{10}">
                            </div>
                            <div class="form-text">رقم الهاتف المحمول (10 أرقام)</div>
                        </div>
                    </div>
                    
                    <!-- Contact Information Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">
                                <i class="bi bi-envelope me-2"></i>
                                معلومات الاتصال
                            </h6>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                <i class="bi bi-envelope me-1"></i>
                                البريد الإلكتروني
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-envelope"></i>
                                </span>
                                <input type="email" class="form-control" name="email" id="email" placeholder="<EMAIL>">
                            </div>
                            <div class="form-text">البريد الإلكتروني (اختياري)</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="city" class="form-label">
                                <i class="bi bi-geo-alt me-1"></i>
                                المدينة
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-geo-alt"></i>
                                </span>
                                <select class="form-select" name="city" id="city">
                                    <option value="">اختر المدينة</option>
                                    <option value="الرياض">الرياض</option>
                                    <option value="جدة">جدة</option>
                                    <option value="الدمام">الدمام</option>
                                    <option value="مكة المكرمة">مكة المكرمة</option>
                                    <option value="المدينة المنورة">المدينة المنورة</option>
                                    <option value="الطائف">الطائف</option>
                                    <option value="أبها">أبها</option>
                                    <option value="حائل">حائل</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                            <div class="form-text">اختر مدينة العميل</div>
                        </div>
                    </div>
                    
                    <!-- Address Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">
                                <i class="bi bi-house me-2"></i>
                                العنوان التفصيلي
                            </h6>
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="address" class="form-label">
                                <i class="bi bi-house me-1"></i>
                                العنوان الكامل
                            </label>
                            <textarea class="form-control" name="address" id="address" rows="3" placeholder="أدخل العنوان التفصيلي (الحي، الشارع، رقم المبنى)"></textarea>
                            <div class="form-text">العنوان التفصيلي للعميل (اختياري)</div>
                        </div>
                    </div>
                    
                    <!-- Additional Information Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">
                                <i class="bi bi-info-circle me-2"></i>
                                معلومات إضافية
                            </h6>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="customer_type" class="form-label">
                                <i class="bi bi-star me-1"></i>
                                نوع العميل
                            </label>
                            <select class="form-select" name="customer_type" id="customer_type">
                                <option value="عادي">عميل عادي</option>
                                <option value="مميز">عميل مميز</option>
                                <option value="VIP">عميل VIP</option>
                            </select>
                            <div class="form-text">تصنيف العميل في النظام</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="notes" class="form-label">
                                <i class="bi bi-chat-text me-1"></i>
                                ملاحظات
                            </label>
                            <textarea class="form-control" name="notes" id="notes" rows="2" placeholder="أي ملاحظات خاصة بالعميل"></textarea>
                            <div class="form-text">ملاحظات إضافية (اختياري)</div>
                        </div>
                    </div>
                    
                    <!-- Form Actions -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center pt-3 border-top">
                                <div class="text-muted">
                                    <small>
                                        <i class="bi bi-info-circle me-1"></i>
                                        الحقول المميزة بـ <span class="text-danger">*</span> مطلوبة
                                    </small>
                                </div>
                                <div>
                                    <a href="{{ url_for('customers') }}" class="btn btn-outline-secondary me-2">
                                        <i class="bi bi-x-circle me-2"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="submitBtn">
                                        <i class="bi bi-check-circle me-2"></i>
                                        حفظ العميل
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Help Card -->
        <div class="card mt-4 border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="bi bi-lightbulb me-2"></i>
                    نصائح مفيدة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-info">معلومات مطلوبة:</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-check-circle text-success me-2"></i>اسم العميل الكامل</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>رقم الهاتف المحمول</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-info">معلومات اختيارية:</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-info-circle text-info me-2"></i>البريد الإلكتروني</li>
                            <li><i class="bi bi-info-circle text-info me-2"></i>العنوان التفصيلي</li>
                            <li><i class="bi bi-info-circle text-info me-2"></i>المدينة والملاحظات</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Simple form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('customerForm');
    const nameInput = document.getElementById('name');
    const phoneInput = document.getElementById('phone');
    const submitBtn = document.getElementById('submitBtn');
    
    // Phone number formatting
    phoneInput.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length > 10) {
            value = value.substring(0, 10);
        }
        e.target.value = value;
    });
    
    // Form submission
    form.addEventListener('submit', function(e) {
        const name = nameInput.value.trim();
        const phone = phoneInput.value.trim();
        
        if (name.length < 3) {
            e.preventDefault();
            alert('يجب أن يكون الاسم 3 أحرف على الأقل');
            nameInput.focus();
            return;
        }
        
        if (phone.length !== 10 || !phone.startsWith('05')) {
            e.preventDefault();
            alert('رقم الهاتف يجب أن يكون 10 أرقام ويبدأ بـ 05');
            phoneInput.focus();
            return;
        }
        
        // Show loading state
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الحفظ...';
        submitBtn.disabled = true;
    });
});
</script>
{% endblock %}
