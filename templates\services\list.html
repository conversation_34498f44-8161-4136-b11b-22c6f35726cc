{% extends "base.html" %}

{% block title %}إدارة الخدمات - مركز صيانة السيارات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-gear me-2"></i>
                إدارة الخدمات
            </h1>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addServiceModal">
                <i class="bi bi-plus-lg me-2"></i>
                إضافة خدمة جديدة
            </button>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if services %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>اسم الخدمة</th>
                                <th>الفئة</th>
                                <th>السعر</th>
                                <th>الوقت المقدر</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for service in services %}
                            <tr>
                                <td>
                                    <strong>{{ service.name }}</strong>
                                    {% if service.description %}
                                    <br><small class="text-muted">{{ service.description[:50] }}...</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if service.category == 'maintenance' %}
                                        <span class="badge bg-info">صيانة دورية</span>
                                    {% elif service.category == 'repair' %}
                                        <span class="badge bg-warning">إصلاح</span>
                                    {% elif service.category == 'replacement' %}
                                        <span class="badge bg-secondary">استبدال قطع غيار</span>
                                    {% endif %}
                                </td>
                                <td class="currency">{{ service.price }}</td>
                                <td>
                                    {% if service.estimated_time %}
                                        {{ service.estimated_time }} دقيقة
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if service.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-gear display-1 text-muted"></i>
                    <h4 class="mt-3">لا توجد خدمات</h4>
                    <p class="text-muted">ابدأ بإضافة خدمة جديدة</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Add Service Modal -->
<div class="modal fade" id="addServiceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة خدمة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="text-muted">سيتم تطوير هذه الميزة قريباً...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
