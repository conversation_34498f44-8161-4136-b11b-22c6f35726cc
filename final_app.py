#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إدارة مركز صيانة السيارات - النسخة النهائية
Car Center Management System - Final Version
"""

from flask import Flask, redirect, url_for, flash, request, session
from datetime import datetime
import webbrowser
import threading
import time

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'car-center-secret-key-2024'

# Simple authentication
def is_logged_in():
    return session.get('logged_in', False)

def require_login():
    if not is_logged_in():
        flash('يرجى تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))
    return None

# Auto-open browser
def open_browser():
    time.sleep(1.5)
    webbrowser.open('http://127.0.0.1:5000')

# Flash messages helper
def get_flash_messages():
    messages = []
    if hasattr(session, '_flashes'):
        for category, message in session._flashes:
            alert_class = 'danger' if category == 'error' else category
            messages.append(f'''
                <div class="alert alert-{alert_class} alert-dismissible fade show" role="alert">
                    <i class="bi bi-info-circle me-2"></i>
                    {message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            ''')
        session._flashes.clear()
    return ''.join(messages)

# Routes
@app.route('/')
def index():
    if is_logged_in():
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if is_logged_in():
        return redirect(url_for('dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if username == 'admin' and password == 'admin123':
            session['logged_in'] = True
            session['username'] = username
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return f'''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تسجيل الدخول - مركز صيانة السيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
        <style>
            body {{ 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                min-height: 100vh; 
                display: flex; 
                align-items: center; 
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }}
            .login-card {{ 
                background: white; 
                border-radius: 20px; 
                padding: 3rem; 
                box-shadow: 0 15px 35px rgba(0,0,0,0.1);
                backdrop-filter: blur(10px);
            }}
            .btn-primary {{ border-radius: 15px; padding: 12px 30px; }}
            .form-control {{ border-radius: 15px; padding: 12px 20px; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-5">
                    <div class="login-card">
                        <div class="text-center mb-4">
                            <i class="bi bi-car-front display-1 text-primary"></i>
                            <h2 class="mt-3">مركز صيانة السيارات</h2>
                            <p class="text-muted">نظام إدارة شامل</p>
                        </div>
                        {get_flash_messages()}
                        <form method="POST">
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="bi bi-person me-2"></i>
                                    اسم المستخدم
                                </label>
                                <input type="text" class="form-control" name="username" required>
                            </div>
                            <div class="mb-4">
                                <label class="form-label">
                                    <i class="bi bi-lock me-2"></i>
                                    كلمة المرور
                                </label>
                                <input type="password" class="form-control" name="password" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100 btn-lg">
                                <i class="bi bi-box-arrow-in-right me-2"></i>
                                دخول
                            </button>
                        </form>
                        <div class="text-center mt-4">
                            <div class="alert alert-info">
                                <small>
                                    <strong>للتجربة:</strong><br>
                                    المستخدم: admin<br>
                                    كلمة المرور: admin123
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    '''

@app.route('/dashboard')
def dashboard():
    auth_check = require_login()
    if auth_check:
        return auth_check
    
    return f'''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>لوحة التحكم - مركز صيانة السيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
        <style>
            body {{ 
                background-color: #f8f9fa; 
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            }}
            .navbar {{ box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
            .card {{ 
                border: none; 
                border-radius: 15px; 
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); 
                transition: transform 0.3s ease;
            }}
            .card:hover {{ transform: translateY(-5px); }}
            .stat-card {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }}
            .stat-card-2 {{ background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; }}
            .stat-card-3 {{ background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; }}
            .stat-card-4 {{ background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; }}
            .btn {{ border-radius: 10px; transition: all 0.3s ease; }}
            .btn:hover {{ transform: translateY(-2px); }}
        </style>
    </head>
    <body>
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/dashboard">
                    <i class="bi bi-car-front me-2"></i>
                    مركز صيانة السيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/customers">
                        <i class="bi bi-people me-1"></i>
                        العملاء
                    </a>
                    <a class="nav-link" href="/maintenance">
                        <i class="bi bi-tools me-1"></i>
                        الصيانة
                    </a>
                    <a class="nav-link" href="/finance">
                        <i class="bi bi-cash-coin me-1"></i>
                        المالية
                    </a>
                    <a class="nav-link" href="/add_customer">
                        <i class="bi bi-person-plus me-1"></i>
                        إضافة عميل
                    </a>
                    <a class="nav-link" href="/add_vehicle">
                        <i class="bi bi-car-front-fill me-1"></i>
                        إضافة سيارة
                    </a>
                    <a class="nav-link" href="/add_maintenance">
                        <i class="bi bi-tools me-1"></i>
                        طلب صيانة
                    </a>
                    <a class="nav-link" href="/add_invoice">
                        <i class="bi bi-receipt me-1"></i>
                        فاتورة جديدة
                    </a>
                    <a class="nav-link" href="/logout">
                        <i class="bi bi-box-arrow-right me-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <h1 class="mb-1">
                        <i class="bi bi-speedometer2 me-2 text-primary"></i>
                        لوحة التحكم
                    </h1>
                    <p class="text-muted">نظرة عامة على أداء المركز</p>
                </div>
            </div>

            {get_flash_messages()}

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card stat-card">
                        <div class="card-body text-center">
                            <i class="bi bi-people display-4 mb-3"></i>
                            <h3>45</h3>
                            <p class="mb-0">إجمالي العملاء</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card stat-card-2">
                        <div class="card-body text-center">
                            <i class="bi bi-car-front display-4 mb-3"></i>
                            <h3>67</h3>
                            <p class="mb-0">إجمالي السيارات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card stat-card-3">
                        <div class="card-body text-center">
                            <i class="bi bi-clock-history display-4 mb-3"></i>
                            <h3>8</h3>
                            <p class="mb-0">طلبات معلقة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card stat-card-4">
                        <div class="card-body text-center">
                            <i class="bi bi-currency-dollar display-4 mb-3"></i>
                            <h3>25,000</h3>
                            <p class="mb-0">الإيرادات الشهرية</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="bi bi-lightning me-2"></i>
                                إجراءات سريعة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <a href="/add_customer" class="btn btn-outline-primary w-100">
                                        <i class="bi bi-person-plus me-2"></i>
                                        إضافة عميل جديد
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="/add_vehicle" class="btn btn-outline-success w-100">
                                        <i class="bi bi-car-front me-2"></i>
                                        إضافة سيارة
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="/add_maintenance" class="btn btn-outline-warning w-100">
                                        <i class="bi bi-tools me-2"></i>
                                        طلب صيانة جديد
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="/add_invoice" class="btn btn-outline-info w-100">
                                        <i class="bi bi-receipt me-2"></i>
                                        إنشاء فاتورة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="row">
                <div class="col-md-8 mb-4">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="bi bi-clock-history me-2"></i>
                                الأنشطة الحديثة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="list-group list-group-flush">
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-person-plus text-success me-2"></i>
                                        تم إضافة عميل جديد: أحمد محمد
                                    </div>
                                    <small class="text-muted">منذ 5 دقائق</small>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-tools text-warning me-2"></i>
                                        طلب صيانة جديد: تويوتا كامري 2020
                                    </div>
                                    <small class="text-muted">منذ 15 دقيقة</small>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-check-circle text-success me-2"></i>
                                        تم إكمال صيانة: هوندا أكورد 2019
                                    </div>
                                    <small class="text-muted">منذ 30 دقيقة</small>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-receipt text-info me-2"></i>
                                        تم إنشاء فاتورة جديدة: 1,500 ريال
                                    </div>
                                    <small class="text-muted">منذ ساعة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-4">
                    <div class="card">
                        <div class="card-header bg-warning text-white">
                            <h5 class="mb-0">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                تنبيهات المخزون
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-warning">
                                <strong>زيت المحرك</strong><br>
                                الكمية: 5 (الحد الأدنى: 10)
                            </div>
                            <div class="alert alert-warning">
                                <strong>فلتر الهواء</strong><br>
                                الكمية: 3 (الحد الأدنى: 15)
                            </div>
                            <div class="alert alert-danger">
                                <strong>فحمات الفرامل</strong><br>
                                الكمية: 2 (الحد الأدنى: 8)
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    '''

@app.route('/add_customer', methods=['GET', 'POST'])
def add_customer():
    auth_check = require_login()
    if auth_check:
        return auth_check

    if request.method == 'POST':
        name = request.form.get('name')
        phone = request.form.get('phone')
        email = request.form.get('email')
        address = request.form.get('address')
        city = request.form.get('city')
        customer_type = request.form.get('customer_type')
        notes = request.form.get('notes')

        # التحقق من صحة البيانات
        errors = []

        if not name or len(name.strip()) < 3:
            errors.append('اسم العميل مطلوب ويجب أن يكون 3 أحرف على الأقل')

        if not phone or len(phone) != 10 or not phone.startswith('05'):
            errors.append('رقم الهاتف يجب أن يكون 10 أرقام ويبدأ بـ 05')

        if email and '@' not in email:
            errors.append('البريد الإلكتروني غير صحيح')

        if errors:
            for error in errors:
                flash(error, 'error')
        else:
            # في التطبيق الحقيقي، سيتم حفظ جميع البيانات في قاعدة البيانات
            success_msg = f'تم إضافة العميل {name} بنجاح!'
            if city:
                success_msg += f' من {city}'
            if customer_type and customer_type != 'عادي':
                success_msg += f' كعميل {customer_type}'

            flash(success_msg, 'success')
            return redirect(url_for('dashboard'))

    return f'''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إضافة عميل جديد - مركز صيانة السيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
        <style>
            body {{ background-color: #f8f9fa; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }}
            .navbar {{ box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
            .card {{ border: none; border-radius: 15px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); }}
            .btn {{ border-radius: 10px; transition: all 0.3s ease; }}
            .btn:hover {{ transform: translateY(-2px); }}
            .form-control, .form-select {{ border-radius: 10px; border: 2px solid #e9ecef; }}
            .form-control:focus, .form-select:focus {{ border-color: #0d6efd; box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25); }}
            .input-group-text {{ border-radius: 10px 0 0 10px; background: #f8f9fa; }}
            .input-group .form-control {{ border-radius: 0 10px 10px 0; }}
        </style>
    </head>
    <body>
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/dashboard">
                    <i class="bi bi-car-front me-2"></i>
                    مركز صيانة السيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/dashboard">
                        <i class="bi bi-speedometer2 me-1"></i>
                        لوحة التحكم
                    </a>
                    <a class="nav-link" href="/customers">
                        <i class="bi bi-people me-1"></i>
                        العملاء
                    </a>
                    <a class="nav-link" href="/maintenance">
                        <i class="bi bi-tools me-1"></i>
                        الصيانة
                    </a>
                    <a class="nav-link" href="/finance">
                        <i class="bi bi-cash-coin me-1"></i>
                        المالية
                    </a>
                    <a class="nav-link" href="/add_vehicle">
                        <i class="bi bi-car-front-fill me-1"></i>
                        إضافة سيارة
                    </a>
                    <a class="nav-link" href="/add_maintenance">
                        <i class="bi bi-tools me-1"></i>
                        طلب صيانة
                    </a>
                    <a class="nav-link" href="/add_invoice">
                        <i class="bi bi-receipt me-1"></i>
                        فاتورة جديدة
                    </a>
                    <a class="nav-link" href="/logout">
                        <i class="bi bi-box-arrow-right me-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="mb-1">
                                <i class="bi bi-person-plus me-2 text-primary"></i>
                                إضافة عميل جديد
                            </h1>
                            <p class="text-muted mb-0">إضافة عميل جديد إلى النظام</p>
                        </div>
                        <div>
                            <a href="/dashboard" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>
                                العودة للوحة التحكم
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            {get_flash_messages()}

            <!-- Form -->
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="bi bi-person-badge me-2"></i>
                                بيانات العميل
                            </h5>
                        </div>
                        <div class="card-body p-4">
                            <form method="POST">
                                <!-- Personal Information -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-primary mb-3">
                                            <i class="bi bi-person-circle me-2"></i>
                                            المعلومات الشخصية
                                        </h6>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">
                                            <i class="bi bi-person me-1"></i>
                                            اسم العميل <span class="text-danger">*</span>
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="bi bi-person"></i>
                                            </span>
                                            <input type="text" class="form-control" name="name" id="name" required placeholder="أدخل الاسم الكامل">
                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">
                                            <i class="bi bi-telephone me-1"></i>
                                            رقم الهاتف <span class="text-danger">*</span>
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="bi bi-telephone"></i>
                                            </span>
                                            <input type="tel" class="form-control" name="phone" id="phone" required placeholder="05xxxxxxxx" maxlength="10">
                                        </div>
                                    </div>
                                </div>

                                <!-- Contact Information -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-primary mb-3">
                                            <i class="bi bi-envelope me-2"></i>
                                            معلومات الاتصال
                                        </h6>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">
                                            <i class="bi bi-envelope me-1"></i>
                                            البريد الإلكتروني
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="bi bi-envelope"></i>
                                            </span>
                                            <input type="email" class="form-control" name="email" id="email" placeholder="<EMAIL>">
                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="city" class="form-label">
                                            <i class="bi bi-geo-alt me-1"></i>
                                            المدينة
                                        </label>
                                        <select class="form-select" name="city" id="city">
                                            <option value="">اختر المدينة</option>
                                            <option value="الرياض">الرياض</option>
                                            <option value="جدة">جدة</option>
                                            <option value="الدمام">الدمام</option>
                                            <option value="مكة المكرمة">مكة المكرمة</option>
                                            <option value="المدينة المنورة">المدينة المنورة</option>
                                            <option value="الطائف">الطائف</option>
                                            <option value="أبها">أبها</option>
                                            <option value="حائل">حائل</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Address -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-primary mb-3">
                                            <i class="bi bi-house me-2"></i>
                                            العنوان
                                        </h6>
                                    </div>

                                    <div class="col-12 mb-3">
                                        <label for="address" class="form-label">
                                            <i class="bi bi-house me-1"></i>
                                            العنوان التفصيلي
                                        </label>
                                        <textarea class="form-control" name="address" id="address" rows="3" placeholder="الحي، الشارع، رقم المبنى"></textarea>
                                    </div>
                                </div>

                                <!-- Additional Info -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-primary mb-3">
                                            <i class="bi bi-info-circle me-2"></i>
                                            معلومات إضافية
                                        </h6>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="customer_type" class="form-label">
                                            <i class="bi bi-star me-1"></i>
                                            نوع العميل
                                        </label>
                                        <select class="form-select" name="customer_type" id="customer_type">
                                            <option value="عادي">عميل عادي</option>
                                            <option value="مميز">عميل مميز</option>
                                            <option value="VIP">عميل VIP</option>
                                        </select>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="notes" class="form-label">
                                            <i class="bi bi-chat-text me-1"></i>
                                            ملاحظات
                                        </label>
                                        <textarea class="form-control" name="notes" id="notes" rows="2" placeholder="ملاحظات إضافية"></textarea>
                                    </div>
                                </div>

                                <!-- Submit -->
                                <div class="text-center pt-3 border-top">
                                    <button type="submit" class="btn btn-primary btn-lg me-2">
                                        <i class="bi bi-check-circle me-2"></i>
                                        حفظ العميل
                                    </button>
                                    <a href="/dashboard" class="btn btn-outline-secondary btn-lg">
                                        <i class="bi bi-x-circle me-2"></i>
                                        إلغاء
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // Phone formatting
            document.getElementById('phone').addEventListener('input', function(e) {{
                let value = e.target.value.replace(/[^0-9]/g, '');
                if (value.length > 10) value = value.substring(0, 10);
                e.target.value = value;
            }});
        </script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    '''

@app.route('/maintenance')
def maintenance():
    auth_check = require_login()
    if auth_check:
        return auth_check

    # بيانات صيانة تجريبية
    maintenance_requests = [
        {
            'id': 1,
            'customer_name': 'أحمد محمد العلي',
            'vehicle': 'تويوتا كامري 2020',
            'service_type': 'صيانة دورية',
            'status': 'قيد التنفيذ',
            'technician': 'محمد الفني',
            'start_date': '2024-01-15',
            'estimated_completion': '2024-01-16',
            'cost': 450.00,
            'priority': 'متوسط'
        },
        {
            'id': 2,
            'customer_name': 'سعد العتيبي',
            'vehicle': 'هوندا أكورد 2019',
            'service_type': 'إصلاح فرامل',
            'status': 'مكتمل',
            'technician': 'أحمد الفني',
            'start_date': '2024-01-14',
            'estimated_completion': '2024-01-14',
            'cost': 320.00,
            'priority': 'عالي'
        },
        {
            'id': 3,
            'customer_name': 'محمد الغامدي',
            'vehicle': 'نيسان التيما 2021',
            'service_type': 'تغيير زيت',
            'status': 'في الانتظار',
            'technician': 'خالد الفني',
            'start_date': '2024-01-16',
            'estimated_completion': '2024-01-16',
            'cost': 180.00,
            'priority': 'منخفض'
        },
        {
            'id': 4,
            'customer_name': 'خالد القحطاني',
            'vehicle': 'هيونداي إلنترا 2018',
            'service_type': 'فحص شامل',
            'status': 'قيد التنفيذ',
            'technician': 'سعد الفني',
            'start_date': '2024-01-15',
            'estimated_completion': '2024-01-17',
            'cost': 250.00,
            'priority': 'متوسط'
        },
        {
            'id': 5,
            'customer_name': 'فهد الشمري',
            'vehicle': 'كيا أوبتيما 2020',
            'service_type': 'إصلاح تكييف',
            'status': 'في الانتظار',
            'technician': 'عبدالله الفني',
            'start_date': '2024-01-17',
            'estimated_completion': '2024-01-18',
            'cost': 380.00,
            'priority': 'عالي'
        }
    ]

    # محاكاة البحث
    search_term = request.args.get('search', '') if request.args else ''
    if search_term:
        maintenance_requests = [r for r in maintenance_requests if search_term.lower() in r['customer_name'].lower() or search_term.lower() in r['vehicle'].lower()]

    # بناء HTML لصفحة الصيانة
    maintenance_html = f'''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إدارة الصيانة - مركز صيانة السيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
        <style>
            body {{ background-color: #f8f9fa; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }}
            .navbar {{ box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
            .card {{ border: none; border-radius: 15px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); }}
            .btn {{ border-radius: 10px; transition: all 0.3s ease; }}
            .btn:hover {{ transform: translateY(-2px); }}
            .table {{ border-radius: 15px; overflow: hidden; }}
            .badge {{ font-size: 0.8em; }}
        </style>
    </head>
    <body>
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/dashboard">
                    <i class="bi bi-car-front me-2"></i>
                    مركز صيانة السيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/dashboard">
                        <i class="bi bi-speedometer2 me-1"></i>
                        لوحة التحكم
                    </a>
                    <a class="nav-link" href="/customers">
                        <i class="bi bi-people me-1"></i>
                        العملاء
                    </a>
                    <a class="nav-link active" href="/maintenance">
                        <i class="bi bi-tools me-1"></i>
                        الصيانة
                    </a>
                    <a class="nav-link" href="/finance">
                        <i class="bi bi-cash-coin me-1"></i>
                        المالية
                    </a>
                    <a class="nav-link" href="/add_customer">
                        <i class="bi bi-person-plus me-1"></i>
                        إضافة عميل
                    </a>
                    <a class="nav-link" href="/add_vehicle">
                        <i class="bi bi-car-front-fill me-1"></i>
                        إضافة سيارة
                    </a>
                    <a class="nav-link" href="/add_maintenance">
                        <i class="bi bi-tools me-1"></i>
                        طلب صيانة
                    </a>
                    <a class="nav-link" href="/add_invoice">
                        <i class="bi bi-receipt me-1"></i>
                        فاتورة جديدة
                    </a>
                    <a class="nav-link" href="/logout">
                        <i class="bi bi-box-arrow-right me-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="mb-1">
                                <i class="bi bi-tools me-2 text-primary"></i>
                                إدارة الصيانة
                            </h1>
                            <p class="text-muted mb-0">متابعة وإدارة جميع طلبات الصيانة</p>
                        </div>
                        <div>
                            <a href="/add_maintenance" class="btn btn-primary me-2">
                                <i class="bi bi-plus-circle me-2"></i>
                                طلب صيانة جديد
                            </a>
                            <button class="btn btn-outline-success" onclick="alert('قيد التطوير')">
                                <i class="bi bi-file-earmark-excel me-2"></i>
                                تصدير التقرير
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {get_flash_messages()}

            <!-- Search -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <form method="GET" class="row g-3">
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-search"></i>
                                        </span>
                                        <input type="text" class="form-control" name="search"
                                               placeholder="البحث بالعميل أو السيارة..."
                                               value="{search_term}">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select">
                                        <option value="">جميع الحالات</option>
                                        <option value="في الانتظار">في الانتظار</option>
                                        <option value="قيد التنفيذ">قيد التنفيذ</option>
                                        <option value="مكتمل">مكتمل</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-outline-primary w-100">
                                        <i class="bi bi-search me-1"></i>
                                        بحث
                                    </button>
                                </div>
                                <div class="col-md-2">
                                    <a href="/maintenance" class="btn btn-outline-secondary w-100">
                                        <i class="bi bi-arrow-clockwise me-1"></i>
                                        إعادة تعيين
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-clock-history display-6 mb-2"></i>
                            <h4>{len([r for r in maintenance_requests if r['status'] == 'في الانتظار'])}</h4>
                            <p class="mb-0">في الانتظار</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-gear display-6 mb-2"></i>
                            <h4>{len([r for r in maintenance_requests if r['status'] == 'قيد التنفيذ'])}</h4>
                            <p class="mb-0">قيد التنفيذ</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-check-circle display-6 mb-2"></i>
                            <h4>{len([r for r in maintenance_requests if r['status'] == 'مكتمل'])}</h4>
                            <p class="mb-0">مكتمل</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-currency-dollar display-6 mb-2"></i>
                            <h4>{sum(r['cost'] for r in maintenance_requests):.0f}</h4>
                            <p class="mb-0">إجمالي القيمة</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Maintenance Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="bi bi-table me-2"></i>
                                طلبات الصيانة
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>رقم الطلب</th>
                                            <th>العميل</th>
                                            <th>السيارة</th>
                                            <th>نوع الخدمة</th>
                                            <th>الحالة</th>
                                            <th>الفني</th>
                                            <th>التاريخ</th>
                                            <th>التكلفة</th>
                                            <th>الأولوية</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
    '''

    # إضافة صفوف طلبات الصيانة
    for request in maintenance_requests:
        # تحديد لون الحالة
        status_color = {
            'في الانتظار': 'warning',
            'قيد التنفيذ': 'info',
            'مكتمل': 'success'
        }.get(request['status'], 'secondary')

        # تحديد لون الأولوية
        priority_color = {
            'عالي': 'danger',
            'متوسط': 'warning',
            'منخفض': 'success'
        }.get(request['priority'], 'secondary')

        maintenance_html += f'''
                                        <tr>
                                            <td><strong>#{request['id']:03d}</strong></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-person-circle text-primary me-2"></i>
                                                    <strong>{request['customer_name']}</strong>
                                                </div>
                                            </td>
                                            <td>
                                                <i class="bi bi-car-front text-info me-1"></i>
                                                {request['vehicle']}
                                            </td>
                                            <td>
                                                <i class="bi bi-tools text-warning me-1"></i>
                                                {request['service_type']}
                                            </td>
                                            <td>
                                                <span class="badge bg-{status_color}">
                                                    {request['status']}
                                                </span>
                                            </td>
                                            <td>
                                                <i class="bi bi-person-gear text-success me-1"></i>
                                                {request['technician']}
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <i class="bi bi-calendar me-1"></i>
                                                    {request['start_date']}
                                                </small>
                                            </td>
                                            <td>
                                                <strong class="text-success">
                                                    {request['cost']:.2f} ريال
                                                </strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-{priority_color}">
                                                    {request['priority']}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" title="عرض التفاصيل">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-warning" title="تعديل">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success" title="تحديث الحالة">
                                                        <i class="bi bi-arrow-repeat"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
        '''

    maintenance_html += '''
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    '''

    return maintenance_html

@app.route('/finance')
def finance():
    auth_check = require_login()
    if auth_check:
        return auth_check

    # بيانات مالية تجريبية
    invoices = [
        {
            'id': 1,
            'invoice_number': 'INV-2024-001',
            'customer_name': 'أحمد محمد العلي',
            'service_description': 'صيانة دورية - تويوتا كامري',
            'amount': 450.00,
            'tax': 67.50,
            'total': 517.50,
            'status': 'مدفوع',
            'payment_method': 'نقدي',
            'date': '2024-01-15',
            'due_date': '2024-01-30'
        },
        {
            'id': 2,
            'invoice_number': 'INV-2024-002',
            'customer_name': 'سعد العتيبي',
            'service_description': 'إصلاح فرامل - هوندا أكورد',
            'amount': 320.00,
            'tax': 48.00,
            'total': 368.00,
            'status': 'مدفوع',
            'payment_method': 'بطاقة ائتمان',
            'date': '2024-01-14',
            'due_date': '2024-01-29'
        },
        {
            'id': 3,
            'invoice_number': 'INV-2024-003',
            'customer_name': 'محمد الغامدي',
            'service_description': 'تغيير زيت - نيسان التيما',
            'amount': 180.00,
            'tax': 27.00,
            'total': 207.00,
            'status': 'معلق',
            'payment_method': 'تحويل بنكي',
            'date': '2024-01-16',
            'due_date': '2024-01-31'
        },
        {
            'id': 4,
            'invoice_number': 'INV-2024-004',
            'customer_name': 'خالد القحطاني',
            'service_description': 'فحص شامل - هيونداي إلنترا',
            'amount': 250.00,
            'tax': 37.50,
            'total': 287.50,
            'status': 'مرسل',
            'payment_method': 'نقدي',
            'date': '2024-01-15',
            'due_date': '2024-01-30'
        },
        {
            'id': 5,
            'invoice_number': 'INV-2024-005',
            'customer_name': 'فهد الشمري',
            'service_description': 'إصلاح تكييف - كيا أوبتيما',
            'amount': 380.00,
            'tax': 57.00,
            'total': 437.00,
            'status': 'مسودة',
            'payment_method': 'بطاقة ائتمان',
            'date': '2024-01-17',
            'due_date': '2024-02-01'
        }
    ]

    # محاكاة البحث
    search_term = request.args.get('search', '') if request.args else ''
    if search_term:
        invoices = [i for i in invoices if search_term.lower() in i['customer_name'].lower() or search_term in i['invoice_number']]

    # بناء HTML لصفحة المالية
    finance_html = f'''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إدارة المالية - مركز صيانة السيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
        <style>
            body {{ background-color: #f8f9fa; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }}
            .navbar {{ box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
            .card {{ border: none; border-radius: 15px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); }}
            .btn {{ border-radius: 10px; transition: all 0.3s ease; }}
            .btn:hover {{ transform: translateY(-2px); }}
            .table {{ border-radius: 15px; overflow: hidden; }}
            .badge {{ font-size: 0.8em; }}
        </style>
    </head>
    <body>
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/dashboard">
                    <i class="bi bi-car-front me-2"></i>
                    مركز صيانة السيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/dashboard">
                        <i class="bi bi-speedometer2 me-1"></i>
                        لوحة التحكم
                    </a>
                    <a class="nav-link" href="/customers">
                        <i class="bi bi-people me-1"></i>
                        العملاء
                    </a>
                    <a class="nav-link" href="/maintenance">
                        <i class="bi bi-tools me-1"></i>
                        الصيانة
                    </a>
                    <a class="nav-link active" href="/finance">
                        <i class="bi bi-cash-coin me-1"></i>
                        المالية
                    </a>
                    <a class="nav-link" href="/add_customer">
                        <i class="bi bi-person-plus me-1"></i>
                        إضافة عميل
                    </a>
                    <a class="nav-link" href="/add_vehicle">
                        <i class="bi bi-car-front-fill me-1"></i>
                        إضافة سيارة
                    </a>
                    <a class="nav-link" href="/add_maintenance">
                        <i class="bi bi-tools me-1"></i>
                        طلب صيانة
                    </a>
                    <a class="nav-link" href="/add_invoice">
                        <i class="bi bi-receipt me-1"></i>
                        فاتورة جديدة
                    </a>
                    <a class="nav-link" href="/logout">
                        <i class="bi bi-box-arrow-right me-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="mb-1">
                                <i class="bi bi-cash-coin me-2 text-primary"></i>
                                إدارة المالية
                            </h1>
                            <p class="text-muted mb-0">متابعة الفواتير والمدفوعات والتقارير المالية</p>
                        </div>
                        <div>
                            <a href="/add_invoice" class="btn btn-primary me-2">
                                <i class="bi bi-plus-circle me-2"></i>
                                فاتورة جديدة
                            </a>
                            <button class="btn btn-outline-success" onclick="alert('قيد التطوير')">
                                <i class="bi bi-file-earmark-pdf me-2"></i>
                                تقرير مالي
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {get_flash_messages()}

            <!-- Search -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <form method="GET" class="row g-3">
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-search"></i>
                                        </span>
                                        <input type="text" class="form-control" name="search"
                                               placeholder="البحث برقم الفاتورة أو اسم العميل..."
                                               value="{search_term}">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select">
                                        <option value="">جميع الحالات</option>
                                        <option value="مسودة">مسودة</option>
                                        <option value="مرسل">مرسل</option>
                                        <option value="معلق">معلق</option>
                                        <option value="مدفوع">مدفوع</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-outline-primary w-100">
                                        <i class="bi bi-search me-1"></i>
                                        بحث
                                    </button>
                                </div>
                                <div class="col-md-2">
                                    <a href="/finance" class="btn btn-outline-secondary w-100">
                                        <i class="bi bi-arrow-clockwise me-1"></i>
                                        إعادة تعيين
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Financial Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-check-circle display-6 mb-2"></i>
                            <h4>{sum(i['total'] for i in invoices if i['status'] == 'مدفوع'):.0f}</h4>
                            <p class="mb-0">المدفوعات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-clock-history display-6 mb-2"></i>
                            <h4>{sum(i['total'] for i in invoices if i['status'] == 'معلق'):.0f}</h4>
                            <p class="mb-0">المعلقة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-file-earmark display-6 mb-2"></i>
                            <h4>{len(invoices)}</h4>
                            <p class="mb-0">إجمالي الفواتير</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-currency-dollar display-6 mb-2"></i>
                            <h4>{sum(i['total'] for i in invoices):.0f}</h4>
                            <p class="mb-0">إجمالي المبلغ</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Invoices Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="bi bi-table me-2"></i>
                                الفواتير والمدفوعات
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>رقم الفاتورة</th>
                                            <th>العميل</th>
                                            <th>الخدمة</th>
                                            <th>المبلغ</th>
                                            <th>الضريبة</th>
                                            <th>الإجمالي</th>
                                            <th>الحالة</th>
                                            <th>طريقة الدفع</th>
                                            <th>التاريخ</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
    '''

    # إضافة صفوف الفواتير
    for invoice in invoices:
        # تحديد لون الحالة
        status_color = {
            'مسودة': 'secondary',
            'مرسل': 'info',
            'معلق': 'warning',
            'مدفوع': 'success'
        }.get(invoice['status'], 'secondary')

        finance_html += f'''
                                        <tr>
                                            <td><strong>{invoice['invoice_number']}</strong></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-person-circle text-primary me-2"></i>
                                                    <strong>{invoice['customer_name']}</strong>
                                                </div>
                                            </td>
                                            <td>
                                                <i class="bi bi-tools text-warning me-1"></i>
                                                {invoice['service_description']}
                                            </td>
                                            <td>
                                                <span class="text-info">
                                                    {invoice['amount']:.2f} ريال
                                                </span>
                                            </td>
                                            <td>
                                                <span class="text-muted">
                                                    {invoice['tax']:.2f} ريال
                                                </span>
                                            </td>
                                            <td>
                                                <strong class="text-success">
                                                    {invoice['total']:.2f} ريال
                                                </strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-{status_color}">
                                                    {invoice['status']}
                                                </span>
                                            </td>
                                            <td>
                                                <i class="bi bi-credit-card text-info me-1"></i>
                                                {invoice['payment_method']}
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <i class="bi bi-calendar me-1"></i>
                                                    {invoice['date']}
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" title="عرض الفاتورة">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-warning" title="تعديل">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success" title="طباعة">
                                                        <i class="bi bi-printer"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
        '''

    finance_html += '''
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    '''

    return finance_html

@app.route('/customers')
def customers():
    auth_check = require_login()
    if auth_check:
        return auth_check

    # بيانات عملاء تجريبية
    mock_customers = [
        {
            'id': 1,
            'name': 'أحمد محمد العلي',
            'phone': '0501234567',
            'email': '<EMAIL>',
            'city': 'الرياض',
            'customer_type': 'مميز',
            'created_at': '2024-01-15',
            'vehicles_count': 2
        },
        {
            'id': 2,
            'name': 'سعد العتيبي',
            'phone': '0509876543',
            'email': '<EMAIL>',
            'city': 'جدة',
            'customer_type': 'عادي',
            'created_at': '2024-01-10',
            'vehicles_count': 1
        },
        {
            'id': 3,
            'name': 'محمد الغامدي',
            'phone': '0551234567',
            'email': '<EMAIL>',
            'city': 'الدمام',
            'customer_type': 'VIP',
            'created_at': '2024-01-08',
            'vehicles_count': 3
        },
        {
            'id': 4,
            'name': 'خالد القحطاني',
            'phone': '0561234567',
            'email': '<EMAIL>',
            'city': 'مكة المكرمة',
            'customer_type': 'عادي',
            'created_at': '2024-01-05',
            'vehicles_count': 1
        },
        {
            'id': 5,
            'name': 'فهد الشمري',
            'phone': '0571234567',
            'email': '<EMAIL>',
            'city': 'المدينة المنورة',
            'customer_type': 'مميز',
            'created_at': '2024-01-03',
            'vehicles_count': 2
        },
        {
            'id': 6,
            'name': 'عبدالله الدوسري',
            'phone': '0581234567',
            'email': '<EMAIL>',
            'city': 'الطائف',
            'customer_type': 'عادي',
            'created_at': '2023-12-28',
            'vehicles_count': 1
        }
    ]

    # محاكاة البحث
    search_term = request.args.get('search', '') if request.args else ''
    if search_term:
        mock_customers = [c for c in mock_customers if search_term.lower() in c['name'].lower() or search_term in c['phone']]

    # بناء HTML للجدول
    customers_html = f'''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>قائمة العملاء - مركز صيانة السيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
        <style>
            body {{ background-color: #f8f9fa; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }}
            .navbar {{ box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
            .card {{ border: none; border-radius: 15px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); }}
            .btn {{ border-radius: 10px; transition: all 0.3s ease; }}
            .btn:hover {{ transform: translateY(-2px); }}
            .table {{ border-radius: 15px; overflow: hidden; }}
            .badge {{ font-size: 0.8em; }}
        </style>
    </head>
    <body>
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/dashboard">
                    <i class="bi bi-car-front me-2"></i>
                    مركز صيانة السيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/dashboard">
                        <i class="bi bi-speedometer2 me-1"></i>
                        لوحة التحكم
                    </a>
                    <a class="nav-link active" href="/customers">
                        <i class="bi bi-people me-1"></i>
                        العملاء
                    </a>
                    <a class="nav-link" href="/maintenance">
                        <i class="bi bi-tools me-1"></i>
                        الصيانة
                    </a>
                    <a class="nav-link" href="/finance">
                        <i class="bi bi-cash-coin me-1"></i>
                        المالية
                    </a>
                    <a class="nav-link" href="/add_customer">
                        <i class="bi bi-person-plus me-1"></i>
                        إضافة عميل
                    </a>
                    <a class="nav-link" href="/add_vehicle">
                        <i class="bi bi-car-front-fill me-1"></i>
                        إضافة سيارة
                    </a>
                    <a class="nav-link" href="/add_maintenance">
                        <i class="bi bi-tools me-1"></i>
                        طلب صيانة
                    </a>
                    <a class="nav-link" href="/add_invoice">
                        <i class="bi bi-receipt me-1"></i>
                        فاتورة جديدة
                    </a>
                    <a class="nav-link" href="/logout">
                        <i class="bi bi-box-arrow-right me-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="mb-1">
                                <i class="bi bi-people me-2 text-primary"></i>
                                قائمة العملاء
                            </h1>
                            <p class="text-muted mb-0">إدارة وعرض جميع العملاء</p>
                        </div>
                        <div>
                            <a href="/add_customer" class="btn btn-primary">
                                <i class="bi bi-person-plus me-2"></i>
                                إضافة عميل جديد
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            {get_flash_messages()}

            <!-- Search -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <form method="GET" class="row g-3">
                                <div class="col-md-8">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-search"></i>
                                        </span>
                                        <input type="text" class="form-control" name="search"
                                               placeholder="البحث بالاسم أو رقم الهاتف..."
                                               value="{search_term}">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-outline-primary w-100">
                                        <i class="bi bi-search me-1"></i>
                                        بحث
                                    </button>
                                </div>
                                <div class="col-md-2">
                                    <a href="/customers" class="btn btn-outline-secondary w-100">
                                        <i class="bi bi-arrow-clockwise me-1"></i>
                                        إعادة تعيين
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-people display-6 mb-2"></i>
                            <h4>{len(mock_customers)}</h4>
                            <p class="mb-0">إجمالي العملاء</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-star display-6 mb-2"></i>
                            <h4>{len([c for c in mock_customers if c['customer_type'] == 'VIP'])}</h4>
                            <p class="mb-0">عملاء VIP</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-award display-6 mb-2"></i>
                            <h4>{len([c for c in mock_customers if c['customer_type'] == 'مميز'])}</h4>
                            <p class="mb-0">عملاء مميزون</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-car-front display-6 mb-2"></i>
                            <h4>{sum(c['vehicles_count'] for c in mock_customers)}</h4>
                            <p class="mb-0">إجمالي السيارات</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customers Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="bi bi-table me-2"></i>
                                بيانات العملاء
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الرقم</th>
                                            <th>اسم العميل</th>
                                            <th>رقم الهاتف</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>المدينة</th>
                                            <th>نوع العميل</th>
                                            <th>عدد السيارات</th>
                                            <th>تاريخ التسجيل</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
    '''

    # إضافة صفوف العملاء
    for customer in mock_customers:
        # تحديد لون نوع العميل
        type_color = {
            'VIP': 'danger',
            'مميز': 'warning',
            'عادي': 'secondary'
        }.get(customer['customer_type'], 'secondary')

        customers_html += f'''
                                        <tr>
                                            <td><strong>#{customer['id']}</strong></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-person-circle text-primary me-2"></i>
                                                    <strong>{customer['name']}</strong>
                                                </div>
                                            </td>
                                            <td>
                                                <i class="bi bi-telephone text-success me-1"></i>
                                                {customer['phone']}
                                            </td>
                                            <td>
                                                <i class="bi bi-envelope text-info me-1"></i>
                                                {customer['email']}
                                            </td>
                                            <td>
                                                <i class="bi bi-geo-alt text-warning me-1"></i>
                                                {customer['city']}
                                            </td>
                                            <td>
                                                <span class="badge bg-{type_color}">
                                                    {customer['customer_type']}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    {customer['vehicles_count']} سيارة
                                                </span>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <i class="bi bi-calendar me-1"></i>
                                                    {customer['created_at']}
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" title="عرض">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-warning" title="تعديل">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" title="حذف"
                                                            onclick="confirmDelete('{customer['name']}')">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
        '''

    customers_html += '''
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pagination -->
            <div class="row mt-4">
                <div class="col-12">
                    <nav>
                        <ul class="pagination justify-content-center">
                            <li class="page-item disabled">
                                <span class="page-link">السابق</span>
                            </li>
                            <li class="page-item active">
                                <span class="page-link">1</span>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">2</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">3</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">التالي</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>

        <script>
            function confirmDelete(customerName) {
                if (confirm('هل أنت متأكد من حذف العميل: ' + customerName + '؟')) {
                    alert('تم حذف العميل بنجاح (تجريبي)');
                }
            }
        </script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    '''

    return customers_html

@app.route('/add_vehicle', methods=['GET', 'POST'])
def add_vehicle():
    auth_check = require_login()
    if auth_check:
        return auth_check

    if request.method == 'POST':
        # معالجة إضافة السيارة
        vehicle_data = {
            'owner_name': request.form.get('owner_name'),
            'phone': request.form.get('phone'),
            'make': request.form.get('make'),
            'model': request.form.get('model'),
            'year': request.form.get('year'),
            'color': request.form.get('color'),
            'plate_number': request.form.get('plate_number'),
            'vin': request.form.get('vin'),
            'engine_type': request.form.get('engine_type'),
            'transmission': request.form.get('transmission'),
            'mileage': request.form.get('mileage'),
            'fuel_type': request.form.get('fuel_type'),
            'notes': request.form.get('notes')
        }

        # محاكاة حفظ البيانات
        flash(f'تم إضافة السيارة بنجاح: {vehicle_data["make"]} {vehicle_data["model"]} - {vehicle_data["plate_number"]}', 'success')
        return redirect(url_for('add_vehicle'))

    return f'''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إضافة سيارة جديدة - مركز صيانة السيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
        <style>
            body {{ background-color: #f8f9fa; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }}
            .navbar {{ box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
            .card {{ border: none; border-radius: 15px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); }}
            .btn {{ border-radius: 10px; transition: all 0.3s ease; }}
            .btn:hover {{ transform: translateY(-2px); }}
            .form-control, .form-select {{ border-radius: 10px; }}
            .section-header {{ background: linear-gradient(135deg, #007bff, #0056b3); color: white; padding: 15px; border-radius: 10px; margin-bottom: 20px; }}
        </style>
    </head>
    <body>
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/dashboard">
                    <i class="bi bi-car-front me-2"></i>
                    مركز صيانة السيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/dashboard">
                        <i class="bi bi-speedometer2 me-1"></i>
                        لوحة التحكم
                    </a>
                    <a class="nav-link" href="/customers">
                        <i class="bi bi-people me-1"></i>
                        العملاء
                    </a>
                    <a class="nav-link" href="/maintenance">
                        <i class="bi bi-tools me-1"></i>
                        الصيانة
                    </a>
                    <a class="nav-link" href="/finance">
                        <i class="bi bi-cash-coin me-1"></i>
                        المالية
                    </a>
                    <a class="nav-link" href="/add_customer">
                        <i class="bi bi-person-plus me-1"></i>
                        إضافة عميل
                    </a>
                    <a class="nav-link active" href="/add_vehicle">
                        <i class="bi bi-car-front-fill me-1"></i>
                        إضافة سيارة
                    </a>
                    <a class="nav-link" href="/add_maintenance">
                        <i class="bi bi-tools me-1"></i>
                        طلب صيانة
                    </a>
                    <a class="nav-link" href="/add_invoice">
                        <i class="bi bi-receipt me-1"></i>
                        فاتورة جديدة
                    </a>
                    <a class="nav-link" href="/logout">
                        <i class="bi bi-box-arrow-right me-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="mb-1">
                                <i class="bi bi-car-front-fill me-2 text-primary"></i>
                                إضافة سيارة جديدة
                            </h1>
                            <p class="text-muted mb-0">إضافة سيارة جديدة إلى النظام</p>
                        </div>
                        <div>
                            <a href="/dashboard" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>
                                العودة للوحة التحكم
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            {get_flash_messages()}

            <!-- Vehicle Form -->
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <form method="POST" class="needs-validation" novalidate>
                        <!-- معلومات المالك -->
                        <div class="card mb-4">
                            <div class="section-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-person-circle me-2"></i>
                                    معلومات المالك
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="owner_name" class="form-label">
                                            <i class="bi bi-person text-primary me-1"></i>
                                            اسم المالك *
                                        </label>
                                        <input type="text" class="form-control" id="owner_name" name="owner_name" required>
                                        <div class="invalid-feedback">يرجى إدخال اسم المالك</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">
                                            <i class="bi bi-telephone text-success me-1"></i>
                                            رقم الهاتف *
                                        </label>
                                        <input type="tel" class="form-control" id="phone" name="phone" required>
                                        <div class="invalid-feedback">يرجى إدخال رقم الهاتف</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات السيارة الأساسية -->
                        <div class="card mb-4">
                            <div class="section-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-car-front me-2"></i>
                                    معلومات السيارة الأساسية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="make" class="form-label">
                                            <i class="bi bi-building text-info me-1"></i>
                                            الماركة *
                                        </label>
                                        <select class="form-select" id="make" name="make" required>
                                            <option value="">اختر الماركة</option>
                                            <option value="تويوتا">تويوتا</option>
                                            <option value="هوندا">هوندا</option>
                                            <option value="نيسان">نيسان</option>
                                            <option value="هيونداي">هيونداي</option>
                                            <option value="كيا">كيا</option>
                                            <option value="مازدا">مازدا</option>
                                            <option value="ميتسوبيشي">ميتسوبيشي</option>
                                            <option value="سوزوكي">سوزوكي</option>
                                            <option value="فورد">فورد</option>
                                            <option value="شيفروليه">شيفروليه</option>
                                            <option value="بي إم دبليو">بي إم دبليو</option>
                                            <option value="مرسيدس">مرسيدس</option>
                                            <option value="أودي">أودي</option>
                                            <option value="لكزس">لكزس</option>
                                            <option value="إنفينيتي">إنفينيتي</option>
                                        </select>
                                        <div class="invalid-feedback">يرجى اختيار الماركة</div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="model" class="form-label">
                                            <i class="bi bi-car-front-fill text-warning me-1"></i>
                                            الموديل *
                                        </label>
                                        <input type="text" class="form-control" id="model" name="model" required>
                                        <div class="invalid-feedback">يرجى إدخال الموديل</div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="year" class="form-label">
                                            <i class="bi bi-calendar text-danger me-1"></i>
                                            سنة الصنع *
                                        </label>
                                        <select class="form-select" id="year" name="year" required>
                                            <option value="">اختر السنة</option>
                                        </select>
                                        <div class="invalid-feedback">يرجى اختيار سنة الصنع</div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="color" class="form-label">
                                            <i class="bi bi-palette text-primary me-1"></i>
                                            اللون
                                        </label>
                                        <select class="form-select" id="color" name="color">
                                            <option value="">اختر اللون</option>
                                            <option value="أبيض">أبيض</option>
                                            <option value="أسود">أسود</option>
                                            <option value="فضي">فضي</option>
                                            <option value="رمادي">رمادي</option>
                                            <option value="أحمر">أحمر</option>
                                            <option value="أزرق">أزرق</option>
                                            <option value="أخضر">أخضر</option>
                                            <option value="بني">بني</option>
                                            <option value="ذهبي">ذهبي</option>
                                            <option value="برتقالي">برتقالي</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="plate_number" class="form-label">
                                            <i class="bi bi-credit-card text-success me-1"></i>
                                            رقم اللوحة *
                                        </label>
                                        <input type="text" class="form-control" id="plate_number" name="plate_number" required>
                                        <div class="invalid-feedback">يرجى إدخال رقم اللوحة</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- المعلومات التقنية -->
                        <div class="card mb-4">
                            <div class="section-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-gear me-2"></i>
                                    المعلومات التقنية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="vin" class="form-label">
                                            <i class="bi bi-upc text-info me-1"></i>
                                            رقم الشاسيه (VIN)
                                        </label>
                                        <input type="text" class="form-control" id="vin" name="vin">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="engine_type" class="form-label">
                                            <i class="bi bi-cpu text-warning me-1"></i>
                                            نوع المحرك
                                        </label>
                                        <select class="form-select" id="engine_type" name="engine_type">
                                            <option value="">اختر نوع المحرك</option>
                                            <option value="1.0L">1.0 لتر</option>
                                            <option value="1.2L">1.2 لتر</option>
                                            <option value="1.4L">1.4 لتر</option>
                                            <option value="1.6L">1.6 لتر</option>
                                            <option value="1.8L">1.8 لتر</option>
                                            <option value="2.0L">2.0 لتر</option>
                                            <option value="2.4L">2.4 لتر</option>
                                            <option value="2.5L">2.5 لتر</option>
                                            <option value="3.0L">3.0 لتر</option>
                                            <option value="3.5L">3.5 لتر</option>
                                            <option value="4.0L">4.0 لتر</option>
                                            <option value="V6">V6</option>
                                            <option value="V8">V8</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="transmission" class="form-label">
                                            <i class="bi bi-gear-fill text-danger me-1"></i>
                                            ناقل الحركة
                                        </label>
                                        <select class="form-select" id="transmission" name="transmission">
                                            <option value="">اختر ناقل الحركة</option>
                                            <option value="أوتوماتيك">أوتوماتيك</option>
                                            <option value="يدوي">يدوي</option>
                                            <option value="CVT">CVT</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="fuel_type" class="form-label">
                                            <i class="bi bi-fuel-pump text-primary me-1"></i>
                                            نوع الوقود
                                        </label>
                                        <select class="form-select" id="fuel_type" name="fuel_type">
                                            <option value="">اختر نوع الوقود</option>
                                            <option value="بنزين 91">بنزين 91</option>
                                            <option value="بنزين 95">بنزين 95</option>
                                            <option value="ديزل">ديزل</option>
                                            <option value="هايبرد">هايبرد</option>
                                            <option value="كهربائي">كهربائي</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="mileage" class="form-label">
                                            <i class="bi bi-speedometer text-success me-1"></i>
                                            عداد المسافة (كم)
                                        </label>
                                        <input type="number" class="form-control" id="mileage" name="mileage" min="0">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- ملاحظات إضافية -->
                        <div class="card mb-4">
                            <div class="section-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-chat-text me-2"></i>
                                    ملاحظات إضافية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">
                                        <i class="bi bi-journal-text text-info me-1"></i>
                                        ملاحظات
                                    </label>
                                    <textarea class="form-control" id="notes" name="notes" rows="4" placeholder="أي ملاحظات إضافية حول السيارة..."></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="card">
                            <div class="card-body text-center">
                                <button type="submit" class="btn btn-primary btn-lg me-3">
                                    <i class="bi bi-check-circle me-2"></i>
                                    إضافة السيارة
                                </button>
                                <a href="/dashboard" class="btn btn-outline-secondary btn-lg">
                                    <i class="bi bi-x-circle me-2"></i>
                                    إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <script>
            // تعبئة سنوات الصنع
            const yearSelect = document.getElementById('year');
            const currentYear = new Date().getFullYear();
            for (let year = currentYear; year >= 1990; year--) {{
                const option = document.createElement('option');
                option.value = year;
                option.textContent = year;
                yearSelect.appendChild(option);
            }}

            // تنسيق رقم الهاتف
            document.getElementById('phone').addEventListener('input', function(e) {{
                let value = e.target.value.replace(/\\D/g, '');
                if (value.length > 0) {{
                    if (value.length <= 3) {{
                        value = value;
                    }} else if (value.length <= 6) {{
                        value = value.slice(0, 3) + '-' + value.slice(3);
                    }} else {{
                        value = value.slice(0, 3) + '-' + value.slice(3, 6) + '-' + value.slice(6, 10);
                    }}
                }}
                e.target.value = value;
            }});

            // Bootstrap validation
            (function() {{
                'use strict';
                window.addEventListener('load', function() {{
                    var forms = document.getElementsByClassName('needs-validation');
                    var validation = Array.prototype.filter.call(forms, function(form) {{
                        form.addEventListener('submit', function(event) {{
                            if (form.checkValidity() === false) {{
                                event.preventDefault();
                                event.stopPropagation();
                            }}
                            form.classList.add('was-validated');
                        }}, false);
                    }});
                }}, false);
            }})();
        </script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    '''

@app.route('/add_maintenance', methods=['GET', 'POST'])
def add_maintenance():
    auth_check = require_login()
    if auth_check:
        return auth_check

    if request.method == 'POST':
        # معالجة إضافة طلب الصيانة
        maintenance_data = {
            'customer_name': request.form.get('customer_name'),
            'phone': request.form.get('phone'),
            'vehicle_make': request.form.get('vehicle_make'),
            'vehicle_model': request.form.get('vehicle_model'),
            'vehicle_year': request.form.get('vehicle_year'),
            'plate_number': request.form.get('plate_number'),
            'service_type': request.form.get('service_type'),
            'priority': request.form.get('priority'),
            'technician': request.form.get('technician'),
            'estimated_cost': request.form.get('estimated_cost'),
            'description': request.form.get('description'),
            'notes': request.form.get('notes')
        }

        # محاكاة حفظ البيانات
        flash(f'تم إضافة طلب الصيانة بنجاح للعميل: {maintenance_data["customer_name"]} - {maintenance_data["service_type"]}', 'success')
        return redirect(url_for('add_maintenance'))

    return f'''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>طلب صيانة جديد - مركز صيانة السيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
        <style>
            body {{ background-color: #f8f9fa; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }}
            .navbar {{ box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
            .card {{ border: none; border-radius: 15px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); }}
            .btn {{ border-radius: 10px; transition: all 0.3s ease; }}
            .btn:hover {{ transform: translateY(-2px); }}
            .form-control, .form-select {{ border-radius: 10px; }}
            .section-header {{ background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px; border-radius: 10px; margin-bottom: 20px; }}
        </style>
    </head>
    <body>
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/dashboard">
                    <i class="bi bi-car-front me-2"></i>
                    مركز صيانة السيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/dashboard">
                        <i class="bi bi-speedometer2 me-1"></i>
                        لوحة التحكم
                    </a>
                    <a class="nav-link" href="/customers">
                        <i class="bi bi-people me-1"></i>
                        العملاء
                    </a>
                    <a class="nav-link" href="/maintenance">
                        <i class="bi bi-tools me-1"></i>
                        الصيانة
                    </a>
                    <a class="nav-link" href="/finance">
                        <i class="bi bi-cash-coin me-1"></i>
                        المالية
                    </a>
                    <a class="nav-link" href="/add_customer">
                        <i class="bi bi-person-plus me-1"></i>
                        إضافة عميل
                    </a>
                    <a class="nav-link" href="/add_vehicle">
                        <i class="bi bi-car-front-fill me-1"></i>
                        إضافة سيارة
                    </a>
                    <a class="nav-link active" href="/add_maintenance">
                        <i class="bi bi-tools me-1"></i>
                        طلب صيانة
                    </a>
                    <a class="nav-link" href="/add_invoice">
                        <i class="bi bi-receipt me-1"></i>
                        فاتورة جديدة
                    </a>
                    <a class="nav-link" href="/logout">
                        <i class="bi bi-box-arrow-right me-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="mb-1">
                                <i class="bi bi-tools me-2 text-success"></i>
                                طلب صيانة جديد
                            </h1>
                            <p class="text-muted mb-0">إضافة طلب صيانة جديد إلى النظام</p>
                        </div>
                        <div>
                            <a href="/maintenance" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>
                                العودة لإدارة الصيانة
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            {get_flash_messages()}

            <!-- Maintenance Form -->
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <form method="POST" class="needs-validation" novalidate>
                        <!-- معلومات العميل -->
                        <div class="card mb-4">
                            <div class="section-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-person-circle me-2"></i>
                                    معلومات العميل
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="customer_name" class="form-label">
                                            <i class="bi bi-person text-primary me-1"></i>
                                            اسم العميل *
                                        </label>
                                        <input type="text" class="form-control" id="customer_name" name="customer_name" required>
                                        <div class="invalid-feedback">يرجى إدخال اسم العميل</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">
                                            <i class="bi bi-telephone text-success me-1"></i>
                                            رقم الهاتف *
                                        </label>
                                        <input type="tel" class="form-control" id="phone" name="phone" required>
                                        <div class="invalid-feedback">يرجى إدخال رقم الهاتف</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات السيارة -->
                        <div class="card mb-4">
                            <div class="section-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-car-front me-2"></i>
                                    معلومات السيارة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <label for="vehicle_make" class="form-label">
                                            <i class="bi bi-building text-info me-1"></i>
                                            الماركة *
                                        </label>
                                        <select class="form-select" id="vehicle_make" name="vehicle_make" required>
                                            <option value="">اختر الماركة</option>
                                            <option value="تويوتا">تويوتا</option>
                                            <option value="هوندا">هوندا</option>
                                            <option value="نيسان">نيسان</option>
                                            <option value="هيونداي">هيونداي</option>
                                            <option value="كيا">كيا</option>
                                            <option value="مازدا">مازدا</option>
                                            <option value="ميتسوبيشي">ميتسوبيشي</option>
                                            <option value="سوزوكي">سوزوكي</option>
                                            <option value="فورد">فورد</option>
                                            <option value="شيفروليه">شيفروليه</option>
                                            <option value="بي إم دبليو">بي إم دبليو</option>
                                            <option value="مرسيدس">مرسيدس</option>
                                            <option value="أودي">أودي</option>
                                            <option value="لكزس">لكزس</option>
                                            <option value="إنفينيتي">إنفينيتي</option>
                                        </select>
                                        <div class="invalid-feedback">يرجى اختيار الماركة</div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label for="vehicle_model" class="form-label">
                                            <i class="bi bi-car-front-fill text-warning me-1"></i>
                                            الموديل *
                                        </label>
                                        <input type="text" class="form-control" id="vehicle_model" name="vehicle_model" required>
                                        <div class="invalid-feedback">يرجى إدخال الموديل</div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label for="vehicle_year" class="form-label">
                                            <i class="bi bi-calendar text-danger me-1"></i>
                                            سنة الصنع *
                                        </label>
                                        <select class="form-select" id="vehicle_year" name="vehicle_year" required>
                                            <option value="">اختر السنة</option>
                                        </select>
                                        <div class="invalid-feedback">يرجى اختيار سنة الصنع</div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label for="plate_number" class="form-label">
                                            <i class="bi bi-credit-card text-success me-1"></i>
                                            رقم اللوحة *
                                        </label>
                                        <input type="text" class="form-control" id="plate_number" name="plate_number" required>
                                        <div class="invalid-feedback">يرجى إدخال رقم اللوحة</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تفاصيل الصيانة -->
                        <div class="card mb-4">
                            <div class="section-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-gear me-2"></i>
                                    تفاصيل الصيانة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="service_type" class="form-label">
                                            <i class="bi bi-tools text-warning me-1"></i>
                                            نوع الخدمة *
                                        </label>
                                        <select class="form-select" id="service_type" name="service_type" required>
                                            <option value="">اختر نوع الخدمة</option>
                                            <option value="صيانة دورية">صيانة دورية</option>
                                            <option value="تغيير زيت">تغيير زيت</option>
                                            <option value="فحص شامل">فحص شامل</option>
                                            <option value="إصلاح فرامل">إصلاح فرامل</option>
                                            <option value="إصلاح تكييف">إصلاح تكييف</option>
                                            <option value="إصلاح محرك">إصلاح محرك</option>
                                            <option value="إصلاح ناقل حركة">إصلاح ناقل حركة</option>
                                            <option value="إصلاح كهرباء">إصلاح كهرباء</option>
                                            <option value="إصلاح عجلات">إصلاح عجلات</option>
                                            <option value="صيانة أخرى">صيانة أخرى</option>
                                        </select>
                                        <div class="invalid-feedback">يرجى اختيار نوع الخدمة</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="priority" class="form-label">
                                            <i class="bi bi-exclamation-triangle text-danger me-1"></i>
                                            الأولوية *
                                        </label>
                                        <select class="form-select" id="priority" name="priority" required>
                                            <option value="">اختر الأولوية</option>
                                            <option value="منخفض">منخفض</option>
                                            <option value="متوسط">متوسط</option>
                                            <option value="عالي">عالي</option>
                                            <option value="عاجل">عاجل</option>
                                        </select>
                                        <div class="invalid-feedback">يرجى اختيار الأولوية</div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="technician" class="form-label">
                                            <i class="bi bi-person-gear text-info me-1"></i>
                                            الفني المسؤول
                                        </label>
                                        <select class="form-select" id="technician" name="technician">
                                            <option value="">اختر الفني</option>
                                            <option value="محمد الفني">محمد الفني</option>
                                            <option value="أحمد الفني">أحمد الفني</option>
                                            <option value="خالد الفني">خالد الفني</option>
                                            <option value="سعد الفني">سعد الفني</option>
                                            <option value="عبدالله الفني">عبدالله الفني</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="estimated_cost" class="form-label">
                                            <i class="bi bi-currency-dollar text-success me-1"></i>
                                            التكلفة المتوقعة (ريال)
                                        </label>
                                        <input type="number" class="form-control" id="estimated_cost" name="estimated_cost" min="0" step="0.01">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="description" class="form-label">
                                        <i class="bi bi-chat-text text-primary me-1"></i>
                                        وصف المشكلة *
                                    </label>
                                    <textarea class="form-control" id="description" name="description" rows="3" required placeholder="اشرح المشكلة أو نوع الصيانة المطلوبة..."></textarea>
                                    <div class="invalid-feedback">يرجى وصف المشكلة</div>
                                </div>
                            </div>
                        </div>

                        <!-- ملاحظات إضافية -->
                        <div class="card mb-4">
                            <div class="section-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-journal-text me-2"></i>
                                    ملاحظات إضافية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">
                                        <i class="bi bi-sticky text-info me-1"></i>
                                        ملاحظات
                                    </label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="card">
                            <div class="card-body text-center">
                                <button type="submit" class="btn btn-success btn-lg me-3">
                                    <i class="bi bi-check-circle me-2"></i>
                                    إضافة طلب الصيانة
                                </button>
                                <a href="/maintenance" class="btn btn-outline-secondary btn-lg">
                                    <i class="bi bi-x-circle me-2"></i>
                                    إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <script>
            // تعبئة سنوات الصنع
            const yearSelect = document.getElementById('vehicle_year');
            const currentYear = new Date().getFullYear();
            for (let year = currentYear; year >= 1990; year--) {{
                const option = document.createElement('option');
                option.value = year;
                option.textContent = year;
                yearSelect.appendChild(option);
            }}

            // تنسيق رقم الهاتف
            document.getElementById('phone').addEventListener('input', function(e) {{
                let value = e.target.value.replace(/\\D/g, '');
                if (value.length > 0) {{
                    if (value.length <= 3) {{
                        value = value;
                    }} else if (value.length <= 6) {{
                        value = value.slice(0, 3) + '-' + value.slice(3);
                    }} else {{
                        value = value.slice(0, 3) + '-' + value.slice(3, 6) + '-' + value.slice(6, 10);
                    }}
                }}
                e.target.value = value;
            }});

            // Bootstrap validation
            (function() {{
                'use strict';
                window.addEventListener('load', function() {{
                    var forms = document.getElementsByClassName('needs-validation');
                    var validation = Array.prototype.filter.call(forms, function(form) {{
                        form.addEventListener('submit', function(event) {{
                            if (form.checkValidity() === false) {{
                                event.preventDefault();
                                event.stopPropagation();
                            }}
                            form.classList.add('was-validated');
                        }}, false);
                    }});
                }}, false);
            }})();
        </script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    '''

@app.route('/add_invoice', methods=['GET', 'POST'])
def add_invoice():
    auth_check = require_login()
    if auth_check:
        return auth_check

    if request.method == 'POST':
        # معالجة إنشاء الفاتورة
        invoice_data = {
            'customer_name': request.form.get('customer_name'),
            'phone': request.form.get('phone'),
            'email': request.form.get('email'),
            'vehicle_info': request.form.get('vehicle_info'),
            'service_description': request.form.get('service_description'),
            'amount': float(request.form.get('amount', 0)),
            'tax_rate': float(request.form.get('tax_rate', 15)),
            'payment_method': request.form.get('payment_method'),
            'due_date': request.form.get('due_date'),
            'notes': request.form.get('notes')
        }

        # حساب الضريبة والإجمالي
        tax_amount = invoice_data['amount'] * (invoice_data['tax_rate'] / 100)
        total_amount = invoice_data['amount'] + tax_amount

        # توليد رقم فاتورة
        import datetime
        invoice_number = f"INV-{datetime.datetime.now().strftime('%Y-%m-%d-%H%M%S')}"

        # محاكاة حفظ البيانات
        flash(f'تم إنشاء الفاتورة بنجاح: {invoice_number} - {invoice_data["customer_name"]} - {total_amount:.2f} ريال', 'success')
        return redirect(url_for('add_invoice'))

    return f'''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إنشاء فاتورة جديدة - مركز صيانة السيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
        <style>
            body {{ background-color: #f8f9fa; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }}
            .navbar {{ box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
            .card {{ border: none; border-radius: 15px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); }}
            .btn {{ border-radius: 10px; transition: all 0.3s ease; }}
            .btn:hover {{ transform: translateY(-2px); }}
            .form-control, .form-select {{ border-radius: 10px; }}
            .section-header {{ background: linear-gradient(135deg, #17a2b8, #138496); color: white; padding: 15px; border-radius: 10px; margin-bottom: 20px; }}
            .invoice-preview {{ background: #f8f9fa; border: 2px dashed #dee2e6; border-radius: 10px; padding: 20px; }}
        </style>
    </head>
    <body>
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/dashboard">
                    <i class="bi bi-car-front me-2"></i>
                    مركز صيانة السيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/dashboard">
                        <i class="bi bi-speedometer2 me-1"></i>
                        لوحة التحكم
                    </a>
                    <a class="nav-link" href="/customers">
                        <i class="bi bi-people me-1"></i>
                        العملاء
                    </a>
                    <a class="nav-link" href="/maintenance">
                        <i class="bi bi-tools me-1"></i>
                        الصيانة
                    </a>
                    <a class="nav-link" href="/finance">
                        <i class="bi bi-cash-coin me-1"></i>
                        المالية
                    </a>
                    <a class="nav-link" href="/add_customer">
                        <i class="bi bi-person-plus me-1"></i>
                        إضافة عميل
                    </a>
                    <a class="nav-link" href="/add_vehicle">
                        <i class="bi bi-car-front-fill me-1"></i>
                        إضافة سيارة
                    </a>
                    <a class="nav-link" href="/add_maintenance">
                        <i class="bi bi-tools me-1"></i>
                        طلب صيانة
                    </a>
                    <a class="nav-link active" href="/add_invoice">
                        <i class="bi bi-receipt me-1"></i>
                        فاتورة جديدة
                    </a>
                    <a class="nav-link" href="/logout">
                        <i class="bi bi-box-arrow-right me-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="mb-1">
                                <i class="bi bi-receipt me-2 text-info"></i>
                                إنشاء فاتورة جديدة
                            </h1>
                            <p class="text-muted mb-0">إنشاء فاتورة جديدة للعميل</p>
                        </div>
                        <div>
                            <a href="/finance" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>
                                العودة للمالية
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            {get_flash_messages()}

            <!-- Invoice Form -->
            <div class="row">
                <div class="col-lg-8">
                    <form method="POST" class="needs-validation" novalidate id="invoiceForm">
                        <!-- معلومات العميل -->
                        <div class="card mb-4">
                            <div class="section-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-person-circle me-2"></i>
                                    معلومات العميل
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="customer_name" class="form-label">
                                            <i class="bi bi-person text-primary me-1"></i>
                                            اسم العميل *
                                        </label>
                                        <input type="text" class="form-control" id="customer_name" name="customer_name" required>
                                        <div class="invalid-feedback">يرجى إدخال اسم العميل</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">
                                            <i class="bi bi-telephone text-success me-1"></i>
                                            رقم الهاتف *
                                        </label>
                                        <input type="tel" class="form-control" id="phone" name="phone" required>
                                        <div class="invalid-feedback">يرجى إدخال رقم الهاتف</div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">
                                            <i class="bi bi-envelope text-info me-1"></i>
                                            البريد الإلكتروني
                                        </label>
                                        <input type="email" class="form-control" id="email" name="email">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="vehicle_info" class="form-label">
                                            <i class="bi bi-car-front text-warning me-1"></i>
                                            معلومات السيارة
                                        </label>
                                        <input type="text" class="form-control" id="vehicle_info" name="vehicle_info" placeholder="مثال: تويوتا كامري 2020">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تفاصيل الخدمة -->
                        <div class="card mb-4">
                            <div class="section-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-tools me-2"></i>
                                    تفاصيل الخدمة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="service_description" class="form-label">
                                        <i class="bi bi-chat-text text-primary me-1"></i>
                                        وصف الخدمة *
                                    </label>
                                    <textarea class="form-control" id="service_description" name="service_description" rows="3" required placeholder="اشرح الخدمة المقدمة..."></textarea>
                                    <div class="invalid-feedback">يرجى وصف الخدمة</div>
                                </div>
                            </div>
                        </div>

                        <!-- المعلومات المالية -->
                        <div class="card mb-4">
                            <div class="section-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-currency-dollar me-2"></i>
                                    المعلومات المالية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="amount" class="form-label">
                                            <i class="bi bi-cash text-success me-1"></i>
                                            المبلغ (ريال) *
                                        </label>
                                        <input type="number" class="form-control" id="amount" name="amount" min="0" step="0.01" required>
                                        <div class="invalid-feedback">يرجى إدخال المبلغ</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="tax_rate" class="form-label">
                                            <i class="bi bi-percent text-warning me-1"></i>
                                            نسبة الضريبة (%)
                                        </label>
                                        <input type="number" class="form-control" id="tax_rate" name="tax_rate" value="15" min="0" max="100" step="0.01">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="payment_method" class="form-label">
                                            <i class="bi bi-credit-card text-info me-1"></i>
                                            طريقة الدفع
                                        </label>
                                        <select class="form-select" id="payment_method" name="payment_method">
                                            <option value="">اختر طريقة الدفع</option>
                                            <option value="نقدي">نقدي</option>
                                            <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                                            <option value="تحويل بنكي">تحويل بنكي</option>
                                            <option value="شيك">شيك</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="due_date" class="form-label">
                                            <i class="bi bi-calendar text-danger me-1"></i>
                                            تاريخ الاستحقاق
                                        </label>
                                        <input type="date" class="form-control" id="due_date" name="due_date">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- ملاحظات -->
                        <div class="card mb-4">
                            <div class="section-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-journal-text me-2"></i>
                                    ملاحظات إضافية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">
                                        <i class="bi bi-sticky text-info me-1"></i>
                                        ملاحظات
                                    </label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="card">
                            <div class="card-body text-center">
                                <button type="submit" class="btn btn-info btn-lg me-3">
                                    <i class="bi bi-check-circle me-2"></i>
                                    إنشاء الفاتورة
                                </button>
                                <a href="/finance" class="btn btn-outline-secondary btn-lg">
                                    <i class="bi bi-x-circle me-2"></i>
                                    إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- معاينة الفاتورة -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="bi bi-eye me-2"></i>
                                معاينة الفاتورة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="invoice-preview">
                                <div class="text-center mb-3">
                                    <h6 class="text-primary">مركز صيانة السيارات</h6>
                                    <small class="text-muted">رقم الفاتورة: <span id="preview-invoice-number">INV-XXXX</span></small>
                                </div>

                                <hr>

                                <div class="mb-2">
                                    <strong>العميل:</strong>
                                    <span id="preview-customer" class="text-muted">لم يتم الإدخال</span>
                                </div>

                                <div class="mb-2">
                                    <strong>الهاتف:</strong>
                                    <span id="preview-phone" class="text-muted">لم يتم الإدخال</span>
                                </div>

                                <div class="mb-2">
                                    <strong>السيارة:</strong>
                                    <span id="preview-vehicle" class="text-muted">لم يتم الإدخال</span>
                                </div>

                                <hr>

                                <div class="mb-2">
                                    <strong>الخدمة:</strong>
                                    <div id="preview-service" class="text-muted small">لم يتم الإدخال</div>
                                </div>

                                <hr>

                                <div class="mb-2">
                                    <strong>المبلغ:</strong>
                                    <span id="preview-amount" class="text-success">0.00 ريال</span>
                                </div>

                                <div class="mb-2">
                                    <strong>الضريبة:</strong>
                                    <span id="preview-tax" class="text-warning">0.00 ريال</span>
                                </div>

                                <hr>

                                <div class="mb-2">
                                    <strong>الإجمالي:</strong>
                                    <span id="preview-total" class="text-primary h5">0.00 ريال</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // تحديث معاينة الفاتورة
            function updatePreview() {{
                const customerName = document.getElementById('customer_name').value || 'لم يتم الإدخال';
                const phone = document.getElementById('phone').value || 'لم يتم الإدخال';
                const vehicle = document.getElementById('vehicle_info').value || 'لم يتم الإدخال';
                const service = document.getElementById('service_description').value || 'لم يتم الإدخال';
                const amount = parseFloat(document.getElementById('amount').value) || 0;
                const taxRate = parseFloat(document.getElementById('tax_rate').value) || 15;

                const taxAmount = amount * (taxRate / 100);
                const total = amount + taxAmount;

                document.getElementById('preview-customer').textContent = customerName;
                document.getElementById('preview-phone').textContent = phone;
                document.getElementById('preview-vehicle').textContent = vehicle;
                document.getElementById('preview-service').textContent = service;
                document.getElementById('preview-amount').textContent = amount.toFixed(2) + ' ريال';
                document.getElementById('preview-tax').textContent = taxAmount.toFixed(2) + ' ريال';
                document.getElementById('preview-total').textContent = total.toFixed(2) + ' ريال';

                // تحديث رقم الفاتورة
                const now = new Date();
                const invoiceNumber = 'INV-' + now.getFullYear() + '-' +
                    String(now.getMonth() + 1).padStart(2, '0') + '-' +
                    String(now.getDate()).padStart(2, '0') + '-' +
                    String(now.getHours()).padStart(2, '0') +
                    String(now.getMinutes()).padStart(2, '0');
                document.getElementById('preview-invoice-number').textContent = invoiceNumber;
            }}

            // ربط الأحداث
            document.getElementById('customer_name').addEventListener('input', updatePreview);
            document.getElementById('phone').addEventListener('input', updatePreview);
            document.getElementById('vehicle_info').addEventListener('input', updatePreview);
            document.getElementById('service_description').addEventListener('input', updatePreview);
            document.getElementById('amount').addEventListener('input', updatePreview);
            document.getElementById('tax_rate').addEventListener('input', updatePreview);

            // تنسيق رقم الهاتف
            document.getElementById('phone').addEventListener('input', function(e) {{
                let value = e.target.value.replace(/\\D/g, '');
                if (value.length > 0) {{
                    if (value.length <= 3) {{
                        value = value;
                    }} else if (value.length <= 6) {{
                        value = value.slice(0, 3) + '-' + value.slice(3);
                    }} else {{
                        value = value.slice(0, 3) + '-' + value.slice(3, 6) + '-' + value.slice(6, 10);
                    }}
                }}
                e.target.value = value;
                updatePreview();
            }});

            // تعيين تاريخ الاستحقاق الافتراضي (30 يوم من اليوم)
            const today = new Date();
            const dueDate = new Date(today.getTime() + (30 * 24 * 60 * 60 * 1000));
            document.getElementById('due_date').value = dueDate.toISOString().split('T')[0];

            // Bootstrap validation
            (function() {{
                'use strict';
                window.addEventListener('load', function() {{
                    var forms = document.getElementsByClassName('needs-validation');
                    var validation = Array.prototype.filter.call(forms, function(form) {{
                        form.addEventListener('submit', function(event) {{
                            if (form.checkValidity() === false) {{
                                event.preventDefault();
                                event.stopPropagation();
                            }}
                            form.classList.add('was-validated');
                        }}, false);
                    }});
                }}, false);
            }})();

            // تحديث المعاينة عند تحميل الصفحة
            updatePreview();
        </script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    '''

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

if __name__ == '__main__':
    print("=" * 60)
    print("🚗 نظام إدارة مركز صيانة السيارات - النسخة النهائية")
    print("Car Center Management System - Final Version")
    print("=" * 60)
    print("🚀 بدء تشغيل النظام...")
    print("📍 الرابط: http://127.0.0.1:5000")
    print("👤 المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("🏠 لوحة التحكم + 📝 إضافة العميل")
    print("✅ جميع أخطاء URL تم إصلاحها")
    print("=" * 60)
    print("🌐 سيتم فتح المتصفح تلقائياً...")

    # Start browser in a separate thread
    threading.Thread(target=open_browser, daemon=True).start()

    # Run the app
    app.run(debug=True, host='127.0.0.1', port=5000, use_reloader=False)
